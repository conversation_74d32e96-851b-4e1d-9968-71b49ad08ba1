import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class EmployeeDashboardScreen extends StatefulWidget {
  final String email;

  const EmployeeDashboardScreen({super.key, required this.email});

  @override
  State<EmployeeDashboardScreen> createState() => _EmployeeDashboardScreenState();
}

class _EmployeeDashboardScreenState extends State<EmployeeDashboardScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Stream<QuerySnapshot> _employeeTicketsStream() {
    // Fetch tickets assigned to this employee
    return _firestore
        .collection('tickets')
        .where('assignedTo', isEqualTo: widget.email)
        .snapshots();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Employee Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () {
              // TODO: Implement logout logic, then navigate to login screen
              Navigator.pushReplacementNamed(context, '/');
            },
          ),
        ],
      ),
      body: StreamBuilder<QuerySnapshot>(
        stream: _employeeTicketsStream(),
        builder: (context, snapshot) {
          if (snapshot.hasError) {
            return const Center(child: Text('Error loading tickets.'));
          }
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          final tickets = snapshot.data!.docs;

          if (tickets.isEmpty) {
            return const Center(child: Text('No tickets assigned.'));
          }

          return ListView.builder(
            itemCount: tickets.length,
            itemBuilder: (context, index) {
              final ticket = tickets[index].data() as Map<String, dynamic>;
              return ListTile(
                title: Text('Ticket ID: ${ticket['ticketid'] ?? 'N/A'}'),
                subtitle: Text('Service: ${ticket['service'] ?? 'N/A'}\n'
                    'Org: ${ticket['org'] ?? 'N/A'}\n'
                    'Priority: ${ticket['priority'] ?? 'N/A'}\n'
                    'Problem: ${ticket['problem'] ?? 'N/A'}'),
                isThreeLine: true,
                onTap: () {
                  // TODO: Navigate to ticket detail screen if needed
                },
              );
            },
          );
        },
      ),
    );
  }
}
