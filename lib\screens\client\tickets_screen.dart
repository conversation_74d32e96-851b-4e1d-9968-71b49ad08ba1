import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../common/ticket_detail_screen.dart';
import '../../widgets/ticket_tile.dart';
import 'create_ticket_screen.dart';

class ClientTicketsScreen extends StatefulWidget {
  final String org; // logged-in user's organization
  final String email;
  const ClientTicketsScreen({
    super.key,
    required this.org,
    required this.email,
  });

  @override
  State<ClientTicketsScreen> createState() => _ClientTicketsScreenState();
}

class _ClientTicketsScreenState extends State<ClientTicketsScreen> {
  final _firestore = FirebaseFirestore.instance;

  List<DocumentSnapshot> allTickets = []; // All tickets from Firestore
  List<DocumentSnapshot> filteredTickets = []; // Tickets after applying filters
  List<DocumentSnapshot> visibleTickets = []; // Tickets visible on current page
  bool isLoading = false;
  int currentPage = 1;
  final int perPage = 10;
  int totalPages = 1;

  String filterField = 'Services';
  String searchText = '';
  String sortOrder = 'Latest';
  final _searchController = TextEditingController();

  final filterOptions = ['Services', 'Priority', 'Date'];
  final sortOptions = ['Latest', 'Earliest'];

  Map<String, String> serviceNames = {};
  Map<String, String> clientNames = {};

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      setState(() => searchText = _searchController.text);
    });
    preloadNames().then((_) => _loadAllTickets());
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> preloadNames() async {
    final services = await _firestore.collection('services').get();
    for (var doc in services.docs) {
      serviceNames[doc['shortcode']] = doc['service'];
    }
    final orgs = await _firestore.collection('clients').get();
    for (var doc in orgs.docs) {
      clientNames[doc['shortcode']] = doc['name'];
    }
  }

  Future<void> _loadAllTickets() async {
    setState(() => isLoading = true);

    try {
      final querySnapshot = await _firestore
          .collection('tickets')
          .where('org', isEqualTo: widget.org)
          .orderBy('date', descending: sortOrder == 'Latest')
          .get();

      setState(() {
        allTickets = querySnapshot.docs;
        _applyFilters(); // Apply initial filters
      });
    } catch (e) {
      // Handle error
      print('Error loading tickets: $e');
    } finally {
      setState(() => isLoading = false);
    }
  }

  void _applyFilters() {
    setState(() {
      isLoading = true;
      currentPage = 1;

      final searchTerm = searchText.toLowerCase();

      filteredTickets = allTickets.where((doc) {
        final data = doc.data() as Map<String, dynamic>;
        final serviceName = serviceNames[data['service']] ?? '';
        final priority = data['priority']?.toString().toLowerCase() ?? '';
        final dateStr = (data['date'] as Timestamp?)?.toDate().toString() ?? '';

        switch (filterField) {
          case 'Services':
            return serviceName.toLowerCase().contains(searchTerm);
          case 'Priority':
            return priority.contains(searchTerm);
          case 'Date':
            return dateStr.contains(searchTerm);
          default:
            return true;
        }
      }).toList();

      // Update pagination
      totalPages = (filteredTickets.length / perPage).ceil();
      if (totalPages == 0) totalPages = 1;

      _updateVisibleTickets();
      isLoading = false;
    });
  }

  void _updateVisibleTickets() {
    final startIndex = (currentPage - 1) * perPage;
    final endIndex = startIndex + perPage;

    setState(() {
      visibleTickets = endIndex > filteredTickets.length
          ? filteredTickets.sublist(startIndex)
          : filteredTickets.sublist(startIndex, endIndex);
    });
  }

  void _goToPage(int page) {
    if (page < 1 || page > totalPages || page == currentPage || isLoading)
      return;

    setState(() {
      currentPage = page;
      _updateVisibleTickets();
    });
  }

  void _clearFilters() {
    _searchController.clear();
    setState(() {
      filterField = 'Services';
      searchText = '';
    });
    _applyFilters();
  }

  Widget _buildTicket(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    final ticketId = data['ticketid'] ?? 'N/A';
    final service = serviceNames[data['service']] ?? data['service'];
    final priority = data['priority'] ?? 'N/A';
    final created = (data['date'] as Timestamp?)?.toDate();
    final isClosed =
        (data['status']?.toString().toLowerCase() ?? '') == 'closed';
    final dateStr = created != null
        ? created.toString().split('.')[0]
        : 'No Date';

    return TicketTile(
      ticketId: ticketId,
      title: 'Ticket #$ticketId',
      subtitle: 'Priority: $priority | $service',
      trailing: Text(dateStr, style: TextStyle(fontSize: 12)),
      isClosed: isClosed,
      leading: CircleAvatar(
        backgroundColor: _getPriorityColor(priority),
        child: Text(
          priority.isNotEmpty ? priority[0].toUpperCase() : '?',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      onTap: () => Navigator.push(
        context,
        MaterialPageRoute(
          builder: (_) => TicketDetailScreen(
            ticket: data,
            role: 'Client',
            serviceNames: serviceNames,
            orgNames: clientNames,
            currentUserEmail: widget.email,
          ),
        ),
      ),
    );
  }

  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
        return Colors.redAccent;
      case 'medium':
        return Colors.orangeAccent;
      case 'low':
        return Colors.green;
      default:
        return Colors.blueGrey;
    }
  }

  List<Widget> _buildFilterRow() {
    return [
      Expanded(
        flex: 2,
        child: DropdownButtonFormField<String>(
          value: filterField,
          isExpanded: true,
          decoration: const InputDecoration(
            labelText: 'Filter By',
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 10),
          ),
          onChanged: (v) => setState(() => filterField = v!),
          items: filterOptions
              .map((f) => DropdownMenuItem(value: f, child: Text(f)))
              .toList(),
        ),
      ),
      const SizedBox(width: 8),
      Expanded(
        flex: 4,
        child: TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            labelText: 'Search',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.search),
          ),
        ),
      ),
      const SizedBox(width: 8),
      ElevatedButton.icon(
        icon: const Icon(Icons.filter_alt),
        label: const Text('Apply'),
        onPressed: _applyFilters,
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        ),
      ),
      const SizedBox(width: 8),
      IconButton(
        icon: const Icon(Icons.clear),
        tooltip: 'Clear Filters',
        onPressed: _clearFilters,
      ),
    ];
  }

  List<Widget> _buildFilterColumn() {
    return [
      DropdownButtonFormField<String>(
        value: filterField,
        isExpanded: true,
        decoration: const InputDecoration(
          labelText: 'Filter By',
          border: OutlineInputBorder(),
          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        ),
        onChanged: (v) => setState(() => filterField = v!),
        items: filterOptions
            .map((f) => DropdownMenuItem(value: f, child: Text(f)))
            .toList(),
      ),
      const SizedBox(height: 12),
      TextField(
        controller: _searchController,
        decoration: const InputDecoration(
          labelText: 'Search',
          border: OutlineInputBorder(),
          prefixIcon: Icon(Icons.search),
        ),
      ),
      const SizedBox(height: 12),
      Row(
        children: [
          ElevatedButton.icon(
            icon: const Icon(Icons.filter_alt),
            label: const Text('Apply'),
            onPressed: _applyFilters,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            icon: const Icon(Icons.clear),
            tooltip: 'Clear Filters',
            onPressed: _clearFilters,
          ),
        ],
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = MediaQuery.of(context).size.width < 600;

    return Scaffold(
      appBar: AppBar(
        title: const Text('My Tickets'),
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.sort),
            onSelected: (v) {
              setState(() => sortOrder = v);
              _loadAllTickets();
            },
            itemBuilder: (_) => sortOptions
                .map((v) => PopupMenuItem(value: v, child: Text(v)))
                .toList(),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(
          12,
          12,
          12,
          80,
        ), // Extra space at the bottom
        child: Column(
          children: [
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: isSmallScreen
                    ? Column(children: _buildFilterColumn())
                    : Row(children: _buildFilterRow()),
              ),
            ),
            const SizedBox(height: 12),
            Expanded(
              child: isLoading && visibleTickets.isEmpty
                  ? const Center(child: CircularProgressIndicator())
                  : visibleTickets.isEmpty
                  ? const Center(child: Text('No tickets found'))
                  : ListView.builder(
                      itemCount: visibleTickets.length,
                      itemBuilder: (_, idx) {
                        return _buildTicket(visibleTickets[idx]);
                      },
                    ),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Page $currentPage of $totalPages'),
                Row(
                  children: [
                    OutlinedButton.icon(
                      onPressed: currentPage > 1
                          ? () => _goToPage(currentPage - 1)
                          : null,
                      icon: const Icon(Icons.arrow_back),
                      label: const Text('Prev'),
                    ),
                    const SizedBox(width: 8),
                    OutlinedButton.icon(
                      onPressed: currentPage < totalPages
                          ? () => _goToPage(currentPage + 1)
                          : null,
                      icon: const Icon(Icons.arrow_forward),
                      label: const Text('Next'),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (_) =>
                  CreateTicketScreen(email: widget.email, org: widget.org),
            ),
          );
          if (result == true) {
            await _loadAllTickets();
          }
        },
        child: const Icon(Icons.add),
        tooltip: 'Create Ticket',
      ),
    );
  }
}
