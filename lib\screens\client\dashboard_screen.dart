import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';

class ClientDashboardScreen extends StatefulWidget {
  final String email;
  final String org;

  const ClientDashboardScreen({
    super.key,
    required this.email,
    required this.org,
  });

  @override
  State<ClientDashboardScreen> createState() => _ClientDashboardScreenState();
}

class _ClientDashboardScreenState extends State<ClientDashboardScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  final Map<String, String> serviceNames = {};
  String? selectedFilter;
  String? selectedService;

  int totalEmployees = 0;
  int totalTickets = 0;
  int openTickets = 0;
  int closedTickets = 0;
  int totalUsers = 0;
  int totalConsultants = 0;
  int totalServices = 0;
  List<Map<String, dynamic>> recentTickets = [];

  @override
  void initState() {
    super.initState();
    Future.microtask(() => preloadData());
  }

  Future<void> preloadData() async {
    await _loadServiceNames();
    await _loadStats();
  }

  Future<void> _loadServiceNames() async {
    final cltosrvSnap = await _firestore
        .collection('cltosrv')
        .where('clshortcode', isEqualTo: widget.org)
        .get();

    final serviceCodes = cltosrvSnap.docs
        .map((d) => d['srvshortcode'] as String)
        .toSet();

    final servicesSnap = await _firestore.collection('services').get();
    for (var doc in servicesSnap.docs) {
      final code = doc['shortcode'];
      final name = doc['service'];
      if (serviceCodes.contains(code)) {
        serviceNames[code] = name;
      }
    }
  }

  Future<void> _loadStats() async {
    Query ticketsQuery = _firestore
        .collection('tickets')
        .where('org', isEqualTo: widget.org);

    if (selectedFilter == 'Service' && selectedService != null) {
      ticketsQuery = ticketsQuery.where('service', isEqualTo: selectedService);
    }

    final snap = await ticketsQuery.get();
    final allTickets = snap.docs;

    totalTickets = allTickets.length;
    openTickets = allTickets.where((t) => t['status'] == 'Open').length;
    closedTickets = allTickets.where((t) => t['status'] == 'Closed').length;

    final recentSnap = await ticketsQuery
        .orderBy('date', descending: true)
        .limit(3)
        .get();

    recentTickets = recentSnap.docs
        .map((d) => d.data() as Map<String, dynamic>)
        .toList();

    await _loadUsers();
    await _loadEmployees();
    setState(() {});
  }

  Future<void> _loadEmployees() async {
    final emptoclSnap = await _firestore
        .collection('emptocl')
        .where('clshortcode', isEqualTo: widget.org)
        .get();

    final assignedEmails = emptoclSnap.docs
        .map((d) => d['empemail'] as String)
        .toSet();

    Set<String> filteredEmails = assignedEmails;

    if (selectedFilter == 'Service' && selectedService != null) {
      final emptosrvSnap = await _firestore
          .collection('emptosrv')
          .where('srvshortcode', isEqualTo: selectedService)
          .get();
      final emailsInService = emptosrvSnap.docs
          .map((d) => d['empemail'] as String)
          .toSet();
      filteredEmails = assignedEmails.intersection(emailsInService);
    }

    final usersSnap = await _firestore.collection('users').get();
    final employees = usersSnap.docs.where(
      (u) => (u['role'] != 'Client') && filteredEmails.contains(u['email']),
    );

    totalEmployees = employees.length;

    if (selectedFilter == 'Service' && selectedService != null) {
      totalServices = 1;
    } else {
      totalServices = serviceNames.length;
    }
  }

  Future<void> _loadUsers() async {
    final usersSnap = await _firestore
        .collection('users')
        .where('org', isEqualTo: widget.org)
        .get();

    final usersData = usersSnap.docs.map((d) => d.data()).toList();

    totalUsers = usersData.where((u) => u['role'] == 'Client').length;
    totalConsultants = usersData.where((u) => u['role'] != 'Client').length;
  }

  Widget _buildStatCard(String title, int count, Color color, double width) {
    return SizedBox(
      width: width,
      child: Card(
        color: color,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 10),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircleAvatar(
                radius: 28,
                backgroundColor: Colors.white,
                child: Text(
                  '$count',
                  style: TextStyle(
                    color: color,
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                  ),
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(color: Colors.white, fontSize: 14),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildResponsiveStats(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double screenWidth = constraints.maxWidth;
        int cardsPerRow = screenWidth >= 900
            ? 6
            : screenWidth >= 600
            ? 4
            : 3;
        double spacing = 12;
        double cardWidth =
            (screenWidth - spacing * (cardsPerRow - 1)) / cardsPerRow;

        List<Widget> cards = [
          _buildStatCard(
            'Total \nTickets',
            totalTickets,
            Colors.blue,
            cardWidth,
          ),
          _buildStatCard(
            'Open \nTickets',
            openTickets,
            Colors.orange,
            cardWidth,
          ),
          _buildStatCard(
            'Closed \nTickets',
            closedTickets,
            Colors.green,
            cardWidth,
          ),
          _buildStatCard('My \nUsers', totalUsers, Colors.purple, cardWidth),
          _buildStatCard(
            'Total \nConsultants',
            totalEmployees,
            Colors.teal,
            cardWidth,
          ),
          _buildStatCard(
            'Total \nServices',
            totalServices,
            Colors.indigo,
            cardWidth,
          ),
        ];

        return Wrap(spacing: spacing, runSpacing: spacing, children: cards);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Client Dashboard'),
        actions: [
          // Clear Filter Button (only visible when a filter is active)
          if (selectedFilter != null || selectedService != null)
            IconButton(
              icon: const Icon(Icons.filter_alt_off),
              tooltip: 'Clear Filters',
              onPressed: () {
                setState(() {
                  selectedFilter = null;
                  selectedService = null;
                });
                _loadStats();
              },
            ),
          // Refresh Button
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh Data',
            onPressed: _loadStats,
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () => Navigator.pushReplacementNamed(context, '/'),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                DropdownButton<String>(
                  hint: const Text('Select Filter'),
                  value: selectedFilter,
                  items: [null, 'Service']
                      .map(
                        (f) => DropdownMenuItem(
                          value: f,
                          child: Text(f ?? 'No Filter'),
                        ),
                      )
                      .toList(),
                  onChanged: (val) {
                    setState(() {
                      selectedFilter = val;
                      selectedService = null;
                    });
                    _loadStats();
                  },
                ),
                const SizedBox(width: 10),
                if (selectedFilter == 'Service')
                  DropdownButton<String>(
                    hint: const Text('Select Service'),
                    value: selectedService != null
                        ? serviceNames[selectedService!]
                        : null,
                    items: serviceNames.values
                        .map((v) => DropdownMenuItem(value: v, child: Text(v)))
                        .toList(),
                    onChanged: (val) {
                      setState(() {
                        selectedService = serviceNames.entries
                            .firstWhere((e) => e.value == val)
                            .key;
                      });
                      _loadStats();
                    },
                  ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              flex: 2,
              child: SingleChildScrollView(
                child: _buildResponsiveStats(context),
              ),
            ),
            const SizedBox(height: 10),
            const Text(
              'Recent Tickets',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Expanded(
              flex: 2,
              child: ListView.builder(
                itemCount: recentTickets.length,
                itemBuilder: (context, index) {
                  final ticket = recentTickets[index];
                  final date = (ticket['date'] as Timestamp).toDate();
                  final serviceName =
                      serviceNames[ticket['service']] ?? ticket['service'];

                  return Card(
                    child: ListTile(
                      title: Text('Ticket ID: ${ticket['ticketid']}'),
                      subtitle: Text(
                        'Service: $serviceName\nDate: ${DateFormat.yMd().add_jm().format(date)}',
                      ),
                      isThreeLine: true,
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
