import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class EmployeeClientsScreen extends StatefulWidget {
  final String email;

  const EmployeeClientsScreen({Key? key, required this.email}) : super(key: key);

  @override
  State<EmployeeClientsScreen> createState() => _EmployeeClientsScreenState();
}

class _EmployeeClientsScreenState extends State<EmployeeClientsScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Stream<QuerySnapshot> _getClientsStream() {
    return _firestore
        .collection('users')
        .where('role', isEqualTo: 'Client')
        .snapshots();
  }

  Widget _buildClientTile(DocumentSnapshot document) {
    final data = document.data() as Map<String, dynamic>;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      child: ListTile(
        leading: const CircleAvatar(child: Icon(Icons.person)),
        title: Text(data['name'] ?? 'No Name'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Email: ${data['email'] ?? 'N/A'}'),
            Text('Org: ${data['org'] ?? 'N/A'}'),
            if (data['phone'] != null) Text('Phone: ${data['phone']}'),
          ],
        ),
        isThreeLine: true,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Client List'),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(25),
          child: Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Text(
              'Logged in as: ${widget.email}',
              style: const TextStyle(color: Colors.white70, fontSize: 14),
            ),
          ),
        ),
      ),
      body: StreamBuilder<QuerySnapshot>(
        stream: _getClientsStream(),
        builder: (context, snapshot) {
          if (snapshot.hasError) return const Center(child: Text('Error loading clients'));
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          final clients = snapshot.data!.docs;
          if (clients.isEmpty) {
            return const Center(child: Text('No clients found.'));
          }

          return ListView.builder(
            itemCount: clients.length,
            itemBuilder: (context, index) {
              return _buildClientTile(clients[index]);
            },
          );
        },
      ),
    );
  }
}
