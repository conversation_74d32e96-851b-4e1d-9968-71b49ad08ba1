import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import '../../widgets/user_tile.dart';

class ClientUsersScreen extends StatefulWidget {
  final String org;
  final String email;

  const ClientUsersScreen({Key? key, required this.org, required this.email})
    : super(key: key);

  @override
  State<ClientUsersScreen> createState() => _ClientUsersScreenState();
}

class _ClientUsersScreenState extends State<ClientUsersScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  String? _orgName;

  @override
  void initState() {
    super.initState();
    _fetchClientName();
  }

  Future<void> _fetchClientName() async {
    try {
      final snapshot = await _firestore
          .collection('clients')
          .where('shortcode', isEqualTo: widget.org)
          .limit(1)
          .get();
      if (snapshot.docs.isNotEmpty) {
        setState(() {
          _orgName = snapshot.docs.first.data()['name'] ?? widget.org;
        });
      } else {
        setState(() {
          _orgName = widget.org;
        });
      }
    } catch (e) {
      setState(() {
        _orgName = widget.org;
      });
    }
  }

  Stream<QuerySnapshot> _getOrgUsers() {
    return _firestore
        .collection('users')
        .where('org', isEqualTo: widget.org)
        .where('role', isNotEqualTo: null)
        .snapshots();
  }

  void _showUserDetail(Map<String, dynamic> data) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Padding(
        padding: const EdgeInsets.all(20.0),
        child: Wrap(
          children: [
            Center(
              child: Container(
                height: 5,
                width: 50,
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
            _buildInfoRow(Icons.person, 'Name', data['name'] ?? 'N/A'),
            _buildInfoRow(Icons.email, 'Email', data['email'] ?? 'N/A'),
            _buildInfoRow(Icons.work, 'Role', data['role'] ?? 'N/A'),
            _buildInfoRow(Icons.phone, 'Phone', data['phone'] ?? 'N/A'),
            _buildInfoRow(
              Icons.apartment,
              'Organization',
              _orgName ?? widget.org,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ListTile(
        leading: Icon(icon, color: Colors.blue),
        title: Text(label, style: const TextStyle(fontWeight: FontWeight.bold)),
        subtitle: Text(value),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Users')),
      body: _orgName == null
          ? const Center(child: CircularProgressIndicator())
          : StreamBuilder<QuerySnapshot>(
              stream: _getOrgUsers(),
              builder: (context, snapshot) {
                if (snapshot.hasError) {
                  return const Center(child: Text('Error loading users.'));
                }
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }

                final users = snapshot.data!.docs;
                if (users.isEmpty) {
                  return const Center(
                    child: Text('No users found for your organization.'),
                  );
                }

                return ListView.builder(
                  itemCount: users.length,
                  itemBuilder: (context, index) {
                    final data = users[index].data() as Map<String, dynamic>;
                    return UserTile(
                      name: data['name'] ?? 'No Name',
                      email: data['email'] ?? 'No Email',
                      org: widget.org,
                      orgname: _orgName ?? widget.org,
                      onTap: () => _showUserDetail(data),
                    );
                  },
                );
              },
            ),
    );
  }
}
