import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../common/ticket_detail_screen.dart';
import '../../widgets/ticket_tile.dart';

class AdminTicketsScreen extends StatefulWidget {
  final String email;
  const AdminTicketsScreen({Key? key, required this.email}) : super(key: key);

  @override
  State<AdminTicketsScreen> createState() => _AdminTicketsScreenState();
}

class _AdminTicketsScreenState extends State<AdminTicketsScreen> {
  final _firestore = FirebaseFirestore.instance;

  List<DocumentSnapshot> allTickets = [];
  List<DocumentSnapshot> filteredTickets = [];
  List<DocumentSnapshot> visibleTickets = [];
  bool isLoading = false;
  int currentPage = 1;
  final int perPage = 10;
  int totalPages = 1;

  String filterField = 'All';
  String searchText = '';
  String sortOrder = 'Latest';
  final _searchController = TextEditingController();

  final filterOptions = ['All', 'Organization', 'Services', 'Priority', 'Date'];
  final sortOptions = ['Latest', 'Earliest'];

  Map<String, String> orgNames = {};
  Map<String, String> serviceNames = {};

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      setState(() => searchText = _searchController.text);
    });
    preloadNames().then((_) => _loadAllTickets());
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> preloadNames() async {
    final orgs = await _firestore.collection('clients').get();
    for (var doc in orgs.docs) {
      orgNames[doc['shortcode']] = doc['name'];
    }

    final services = await _firestore.collection('services').get();
    for (var doc in services.docs) {
      serviceNames[doc['shortcode']] = doc['service'];
    }
  }

  Future<void> _loadAllTickets() async {
    setState(() => isLoading = true);

    try {
      final querySnapshot = await _firestore
          .collection('tickets')
          .orderBy('date', descending: sortOrder == 'Latest')
          .get();

      setState(() {
        allTickets = querySnapshot.docs;
        _applyFilters(); // Apply initial filters
      });
    } catch (e) {
      debugPrint('Error loading tickets: $e');
    } finally {
      setState(() => isLoading = false);
    }
  }

  void _applyFilters() {
    setState(() {
      isLoading = true;
      currentPage = 1;

      final searchTerm = searchText.toLowerCase();

      filteredTickets = allTickets.where((doc) {
        final data = doc.data() as Map<String, dynamic>;
        final orgName = orgNames[data['org']] ?? '';
        final serviceName = serviceNames[data['service']] ?? '';
        final priority = data['priority']?.toString().toLowerCase() ?? '';
        final dateStr = (data['date'] as Timestamp?)?.toDate().toString() ?? '';

        switch (filterField) {
          case 'Organization':
            return orgName.toLowerCase().contains(searchTerm);
          case 'Services':
            return serviceName.toLowerCase().contains(searchTerm);
          case 'Priority':
            return priority.contains(searchTerm);
          case 'Date':
            return dateStr.contains(searchTerm);
          default: // 'All'
            return orgName.toLowerCase().contains(searchTerm) ||
                serviceName.toLowerCase().contains(searchTerm) ||
                priority.contains(searchTerm) ||
                dateStr.contains(searchTerm) ||
                (data['ticketid']?.toString().toLowerCase() ?? '').contains(
                  searchTerm,
                );
        }
      }).toList();

      // Update pagination
      totalPages = (filteredTickets.length / perPage).ceil();
      if (totalPages == 0) totalPages = 1;

      _updateVisibleTickets();
      isLoading = false;
    });
  }

  void _updateVisibleTickets() {
    final startIndex = (currentPage - 1) * perPage;
    final endIndex = startIndex + perPage;

    setState(() {
      visibleTickets = endIndex > filteredTickets.length
          ? filteredTickets.sublist(startIndex)
          : filteredTickets.sublist(startIndex, endIndex);
    });
  }

  void _goToPage(int page) {
    if (page < 1 || page > totalPages || page == currentPage || isLoading)
      return;

    setState(() {
      currentPage = page;
      _updateVisibleTickets();
    });
  }

  void _clearFilters() {
    _searchController.clear();
    setState(() {
      filterField = 'All';
      searchText = '';
    });
    _applyFilters();
  }

  Widget _buildTicket(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    final ticketId = data['ticketid'] ?? 'N/A';
    final orgName = orgNames[data['org']] ?? data['org'];
    final serviceName = serviceNames[data['service']] ?? data['service'];
    final priority = data['priority'] ?? 'N/A';
    final created = (data['date'] as Timestamp?)?.toDate();
    final dateStr = created != null
        ? created.toString().split('.')[0]
        : 'No Date';
    final isClosed =
        (data['status']?.toString().toLowerCase() ?? '') == 'closed';

    return TicketTile(
      ticketId: ticketId,
      isClosed: isClosed,
      title: 'Ticket #$ticketId',
      subtitle: 'Priority: $priority | $serviceName | $orgName',
      trailing: Text(dateStr, style: const TextStyle(fontSize: 12)),
      leading: CircleAvatar(
        backgroundColor: _getPriorityColor(priority),
        child: Text(
          priority.isNotEmpty ? priority[0].toUpperCase() : '?',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      onTap: () => Navigator.push(
        context,
        MaterialPageRoute(
          builder: (_) => TicketDetailScreen(
            ticket: data,
            role: 'Admin',
            serviceNames: serviceNames,
            orgNames: orgNames,
            currentUserEmail: widget.email,
          ),
        ),
      ),
    );
  }

  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
        return Colors.redAccent;
      case 'medium':
        return Colors.orangeAccent;
      case 'low':
        return Colors.green;
      default:
        return Colors.blueGrey;
    }
  }

  List<Widget> _buildFilterRow() {
    return [
      Expanded(
        flex: 2,
        child: DropdownButtonFormField<String>(
          value: filterField,
          isExpanded: true,
          decoration: const InputDecoration(
            labelText: 'Filter By',
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 10),
          ),
          onChanged: (v) => setState(() => filterField = v!),
          items: filterOptions
              .map((f) => DropdownMenuItem(value: f, child: Text(f)))
              .toList(),
        ),
      ),
      const SizedBox(width: 8),
      Expanded(
        flex: 4,
        child: TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            labelText: 'Search',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.search),
          ),
        ),
      ),
      const SizedBox(width: 8),
      ElevatedButton.icon(
        icon: const Icon(Icons.filter_alt),
        label: const Text('Apply'),
        onPressed: _applyFilters,
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        ),
      ),
      const SizedBox(width: 8),
      IconButton(
        icon: const Icon(Icons.clear),
        tooltip: 'Clear Filters',
        onPressed: _clearFilters,
      ),
    ];
  }

  List<Widget> _buildFilterColumn() {
    return [
      DropdownButtonFormField<String>(
        value: filterField,
        isExpanded: true,
        decoration: const InputDecoration(
          labelText: 'Filter By',
          border: OutlineInputBorder(),
          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        ),
        onChanged: (v) => setState(() => filterField = v!),
        items: filterOptions
            .map((f) => DropdownMenuItem(value: f, child: Text(f)))
            .toList(),
      ),
      const SizedBox(height: 12),
      TextField(
        controller: _searchController,
        decoration: const InputDecoration(
          labelText: 'Search',
          border: OutlineInputBorder(),
          prefixIcon: Icon(Icons.search),
        ),
      ),
      const SizedBox(height: 12),
      Row(
        children: [
          ElevatedButton.icon(
            icon: const Icon(Icons.filter_alt),
            label: const Text('Apply'),
            onPressed: _applyFilters,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            icon: const Icon(Icons.clear),
            tooltip: 'Clear Filters',
            onPressed: _clearFilters,
          ),
        ],
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = MediaQuery.of(context).size.width < 600;

    return Scaffold(
      appBar: AppBar(
        title: const Text('All Tickets'),
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.sort),
            onSelected: (v) {
              setState(() => sortOrder = v);
              _loadAllTickets();
            },
            itemBuilder: (_) => sortOptions
                .map((v) => PopupMenuItem(value: v, child: Text(v)))
                .toList(),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: isSmallScreen
                    ? Column(children: _buildFilterColumn())
                    : Row(children: _buildFilterRow()),
              ),
            ),
            const SizedBox(height: 12),
            Expanded(
              child: isLoading && visibleTickets.isEmpty
                  ? const Center(child: CircularProgressIndicator())
                  : visibleTickets.isEmpty
                  ? const Center(child: Text('No tickets found'))
                  : ListView.builder(
                      itemCount: visibleTickets.length,
                      itemBuilder: (_, idx) {
                        return _buildTicket(visibleTickets[idx]);
                      },
                    ),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Page $currentPage of $totalPages'),
                Row(
                  children: [
                    OutlinedButton.icon(
                      onPressed: currentPage > 1
                          ? () => _goToPage(currentPage - 1)
                          : null,
                      icon: const Icon(Icons.arrow_back),
                      label: const Text('Prev'),
                    ),
                    const SizedBox(width: 8),
                    OutlinedButton.icon(
                      onPressed: currentPage < totalPages
                          ? () => _goToPage(currentPage + 1)
                          : null,
                      icon: const Icon(Icons.arrow_forward),
                      label: const Text('Next'),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
