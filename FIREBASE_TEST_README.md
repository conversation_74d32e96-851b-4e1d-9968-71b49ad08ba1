# Firebase Firestore Connection Test

## Overview
A temporary test function has been added to `lib/main.dart` to verify Firebase Firestore integration before the app starts.

## Test Function: `testFirestoreConnection()`

### Purpose
- Verify Firebase is properly configured and connected
- Test Firestore database access
- Check if the 'users' collection exists and is accessible
- Validate expected field structure (email, org, pwd, role) in user documents
- Test read and write permissions

### What the Test Does

1. **Connection Test**: Attempts to connect to Firestore
2. **Read Test**: Queries the 'users' collection (limited to 10 documents)
3. **Data Structure Validation**: Checks for expected fields:
   - `email` - User's email address
   - `org` - Organization/company name
   - `pwd` - Password (displayed as [MASKED] for security)
   - `role` - User role (admin, employee, client)
4. **Write Test**: Creates and deletes a test document to verify write permissions
5. **Error Handling**: Provides detailed error messages and suggestions

### Expected Output

#### Successful Connection:
```
🔥 Starting Firebase Firestore connection test...
📡 Attempting to connect to Firestore...
✅ Successfully connected to Firestore!
📊 Found X documents in users collection
📋 Analyzing user documents structure...

--- Document 1 (ID: abc123) ---
  ✅ email: <EMAIL>
  ✅ org: Example Company
  ✅ pwd: [MASKED]
  ✅ role: admin

🔧 Testing write permissions...
✅ Write permissions confirmed
🧹 Test document cleaned up

🎉 Firebase Firestore connection test completed successfully!
🚀 App startup can proceed...
```

#### Empty Collection:
```
🔥 Starting Firebase Firestore connection test...
📡 Attempting to connect to Firestore...
✅ Successfully connected to Firestore!
📊 Found 0 documents in users collection
⚠️  Users collection is empty
💡 This is normal for a new Firebase project
```

#### Connection Error:
```
🔥 Starting Firebase Firestore connection test...
❌ Firebase Firestore connection test failed!
🔍 Error details: [Error message]
💡 Suggestion: [Specific suggestion based on error type]
⚠️  App will continue to start, but Firebase features may not work properly
```

### Configuration

#### Enable/Disable Test
```dart
// In lib/main.dart
const bool enableFirebaseTest = true; // Set to false to disable test
```

#### Remove Test Function
Once Firebase integration is confirmed working:
1. Set `enableFirebaseTest = false`
2. Remove the `testFirestoreConnection()` function
3. Remove the test function call from `main()`
4. Remove this README file

### Common Error Messages and Solutions

| Error Type | Suggestion |
|------------|------------|
| PERMISSION_DENIED | Check Firestore security rules |
| NOT_FOUND | Verify Firebase project configuration |
| UNAVAILABLE | Check internet connection and Firebase project status |
| Other | Check Firebase configuration files (google-services.json, GoogleService-Info.plist) |

### Security Notes
- Password fields are masked in output for security
- Test creates and immediately deletes a temporary document
- No sensitive data is logged or stored during testing

### Next Steps
1. Run the app and check console output
2. Verify Firebase connection is successful
3. If errors occur, follow the suggested solutions
4. Once confirmed working, disable or remove the test function
