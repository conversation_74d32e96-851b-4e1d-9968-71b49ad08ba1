import 'package:cloud_firestore/cloud_firestore.dart';

class AuthService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Authenticate user by checking Firestore `users` collection
  Future<Map<String, dynamic>?> login(String email, String password) async {
    final query = await _firestore
        .collection('users')
        .where('email', isEqualTo: email)
        .where('pwd', isEqualTo: password)
        .limit(1)
        .get();

    if (query.docs.isNotEmpty) {
      return query.docs.first.data(); // Return user info
    } else {
      return null; // Invalid credentials
    }
  }
}
