import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:dropdown_search/dropdown_search.dart';

class Employee {
  String? id;
  String name;
  String email;
  String phone;
  String role;
  String org;

  Employee({
    this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.role,
    required this.org,
  });

  factory Employee.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Employee(
      id: doc.id,
      name: data['name'] ?? '',
      email: data['email'] ?? '',
      phone: data['phone'] ?? '',
      role: data['role'] ?? '',
      org: data['org'] ?? '',
    );
  }

  Map<String, dynamic> toMap({bool includeRoleOrg = true}) {
    final map = {'name': name, 'email': email, 'phone': phone};
    if (includeRoleOrg) {
      map['role'] = role;
      map['org'] = org;
    }
    return map;
  }
}

class AdminEmployeesScreen extends StatefulWidget {
  const AdminEmployeesScreen({Key? key}) : super(key: key);

  @override
  State<AdminEmployeesScreen> createState() => _AdminEmployeesScreenState();
}

class _AdminEmployeesScreenState extends State<AdminEmployeesScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Stream<List<Employee>> getEmployeesStream() {
    // Fetch users where role=employee OR org=own
    return _firestore
        .collection('users')
        .where(
          'role',
          whereIn: ['employee', 'admin'],
        ) // fetch employees and admins, filter later
        .snapshots()
        .map(
          (snapshot) => snapshot.docs
              .map((doc) => Employee.fromFirestore(doc))
              .where(
                (e) => e.role == 'employee' || e.org == 'own',
              ) // filter after fetch (Firestore does not support OR queries well)
              .toList(),
        );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Employees")),
      body: StreamBuilder<List<Employee>>(
        stream: getEmployeesStream(),
        builder: (context, snapshot) {
          if (snapshot.hasError)
            return const Center(child: Text("Error loading employees"));
          if (snapshot.connectionState == ConnectionState.waiting)
            return const Center(child: CircularProgressIndicator());

          final employees = snapshot.data ?? [];
          if (employees.isEmpty)
            return const Center(child: Text("No employees found."));

          return ListView.builder(
            itemCount: employees.length,
            padding: const EdgeInsets.all(8),
            itemBuilder: (context, index) {
              final emp = employees[index];
              return Card(
                elevation: 2,
                margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 4),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ListTile(
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 10,
                  ),
                  leading: CircleAvatar(
                    radius: 24,
                    child: Text(
                      emp.name.isNotEmpty ? emp.name[0].toUpperCase() : '?',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  title: Text(
                    emp.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  subtitle: Text(
                    emp.email,
                    style: const TextStyle(color: Colors.grey),
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 18),
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (_) => EmployeeDetailScreen(employee: emp),
                    ),
                  ),
                ),
              );
            },
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        tooltip: "Add Employee",
        child: const Icon(Icons.add),
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (_) => AddEditEmployeeScreen()),
          );
        },
      ),
    );
  }
}

class EmployeeDetailScreen extends StatefulWidget {
  final Employee employee;
  const EmployeeDetailScreen({Key? key, required this.employee})
    : super(key: key);

  @override
  State<EmployeeDetailScreen> createState() => _EmployeeDetailScreenState();
}

class _EmployeeDetailScreenState extends State<EmployeeDetailScreen> {
  Map<String, String> clientNameMap = {};
  Map<String, String> serviceNameMap = {};
  bool isLoadingClients = true;
  bool isLoadingServices = true;

  @override
  Widget build(BuildContext context) {
    final employee = widget.employee;

    return Scaffold(
      appBar: AppBar(
        title: const Text("Employee Details"),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => AddEditEmployeeScreen(employee: employee),
                ),
              ).then((_) => setState(() {}));
            },
          ),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildEmployeeInfoCard(employee),
              const SizedBox(height: 20),

              Expanded(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    final isSmallScreen = constraints.maxWidth < 600;

                    if (isSmallScreen) {
                      return Column(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildClientHeader(),
                                const Divider(),
                                Expanded(child: _buildClientsColumn()),
                              ],
                            ),
                          ),
                          const SizedBox(height: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildServiceHeader(),
                                const Divider(),
                                Expanded(child: _buildServicesColumn()),
                              ],
                            ),
                          ),
                        ],
                      );
                    } else {
                      return Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildClientHeader(),
                                const Divider(),
                                Expanded(child: _buildClientsColumn()),
                              ],
                            ),
                          ),
                          VerticalDivider(
                            width: 16,
                            thickness: 1,
                            color: Colors.grey.shade300,
                          ),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildServiceHeader(),
                                const Divider(),
                                Expanded(child: _buildServicesColumn()),
                              ],
                            ),
                          ),
                        ],
                      );
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmployeeInfoCard(Employee employee) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _infoRow(
              Icons.person,
              employee.name,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
            const SizedBox(height: 12),
            _infoRow(Icons.email, employee.email),
            const SizedBox(height: 10),
            _infoRow(Icons.phone, employee.phone),
            const SizedBox(height: 10),
            _infoRow(Icons.badge, "Role: ${employee.role}"),
          ],
        ),
      ),
    );
  }

  Widget _infoRow(
    IconData icon,
    String text, {
    double fontSize = 16,
    FontWeight fontWeight = FontWeight.normal,
  }) {
    return Row(
      children: [
        Icon(icon, color: Colors.blue),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: TextStyle(fontSize: fontSize, fontWeight: fontWeight),
          ),
        ),
      ],
    );
  }

  Widget _buildClientHeader() {
    return Row(
      children: const [
        Icon(Icons.people, size: 20),
        SizedBox(width: 6),
        Text(
          "Assigned Clients",
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Widget _buildServiceHeader() {
    return Row(
      children: const [
        Icon(Icons.miscellaneous_services, size: 20),
        SizedBox(width: 6),
        Text(
          "Assigned Services",
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Widget _buildClientsColumn() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: isLoadingClients
              ? const Center(child: CircularProgressIndicator())
              : StreamBuilder<QuerySnapshot>(
                  stream: FirebaseFirestore.instance
                      .collection('emptocl')
                      .where('empemail', isEqualTo: widget.employee.email)
                      .snapshots(),
                  builder: (context, snapshot) {
                    if (!snapshot.hasData) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    final docs = snapshot.data!.docs;

                    if (docs.isEmpty) {
                      return const Center(child: Text("No clients assigned."));
                    }

                    return ListView.builder(
                      itemCount: docs.length,
                      itemBuilder: (context, index) {
                        final doc = docs[index];
                        final short = doc['clshortcode'];
                        final name = clientNameMap[short] ?? short;

                        return _assignmentCard(
                          icon: Icons.business,
                          name: name,
                          shortcode: short,
                          onDelete: () async {
                            await FirebaseFirestore.instance
                                .collection('emptocl')
                                .doc(doc.id)
                                .delete();
                          },
                        );
                      },
                    );
                  },
                ),
        ),
        ModalAssignButton(
          label: "Assign new client",
          items: clientNameMap,
          onSelect: (shortcode) async {
            final exists = await FirebaseFirestore.instance
                .collection('emptocl')
                .where('empemail', isEqualTo: widget.employee.email)
                .where('clshortcode', isEqualTo: shortcode)
                .get();

            if (exists.docs.isEmpty) {
              await FirebaseFirestore.instance.collection('emptocl').add({
                'empemail': widget.employee.email,
                'clshortcode': shortcode,
              });
            }
          },
        ),
        const SizedBox(height: 8),
      ],
    );
  }

  Widget _buildServicesColumn() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: isLoadingServices
              ? const Center(child: CircularProgressIndicator())
              : StreamBuilder<QuerySnapshot>(
                  stream: FirebaseFirestore.instance
                      .collection('emptosrv')
                      .where('empemail', isEqualTo: widget.employee.email)
                      .snapshots(),
                  builder: (context, snapshot) {
                    if (!snapshot.hasData) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    final docs = snapshot.data!.docs;

                    if (docs.isEmpty) {
                      return const Center(child: Text("No services assigned."));
                    }

                    return ListView.builder(
                      itemCount: docs.length,
                      itemBuilder: (context, index) {
                        final doc = docs[index];
                        final short = doc['srvshortcode'];
                        final name = serviceNameMap[short] ?? short;

                        return _assignmentCard(
                          icon: Icons.miscellaneous_services,
                          name: name,
                          shortcode: short,
                          onDelete: () async {
                            await FirebaseFirestore.instance
                                .collection('emptosrv')
                                .doc(doc.id)
                                .delete();
                          },
                        );
                      },
                    );
                  },
                ),
        ),
        ModalAssignButton(
          label: "Assign new service",
          items: serviceNameMap,
          onSelect: (shortcode) async {
            final exists = await FirebaseFirestore.instance
                .collection('emptosrv')
                .where('empemail', isEqualTo: widget.employee.email)
                .where('srvshortcode', isEqualTo: shortcode)
                .get();

            if (exists.docs.isEmpty) {
              await FirebaseFirestore.instance.collection('emptosrv').add({
                'empemail': widget.employee.email,
                'srvshortcode': shortcode,
              });
            }
          },
        ),
        const SizedBox(height: 8),
      ],
    );
  }

  Widget _assignmentCard({
    required IconData icon,
    required String name,
    required String shortcode,
    required VoidCallback onDelete,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 2),
      child: ListTile(
        dense: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        leading: Icon(icon, color: Colors.blue, size: 24),
        title: Text(
          name,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
        subtitle: Text(
          shortcode,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: const TextStyle(fontSize: 12, color: Colors.grey),
        ),
        trailing: IconButton(
          icon: const Icon(Icons.delete, color: Colors.redAccent, size: 20),
          onPressed: onDelete,
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _loadClientsAndServices();
  }

  Future<void> _loadClientsAndServices() async {
    final clientSnap = await FirebaseFirestore.instance
        .collection('clients')
        .get();
    final serviceSnap = await FirebaseFirestore.instance
        .collection('services')
        .get();

    setState(() {
      clientNameMap = {
        for (var doc in clientSnap.docs)
          doc['shortcode'] as String: doc['name'] as String,
      };

      serviceNameMap = {
        for (var doc in serviceSnap.docs)
          doc['shortcode'] as String: doc['service'] as String,
      };

      isLoadingClients = false;
      isLoadingServices = false;
    });
  }
}

class ModalAssignButton extends StatelessWidget {
  final String label;
  final Map<String, String> items;
  final Future<void> Function(String shortcode) onSelect;

  const ModalAssignButton({
    super.key,
    required this.label,
    required this.items,
    required this.onSelect,
  });

  void _openModal(BuildContext context) {
    showDialog(
      context: context,
      builder: (_) {
        String? selectedKey;

        return AlertDialog(
          title: Text(label),
          content: DropdownSearch<String>(
            popupProps: PopupProps.menu(showSearchBox: true),
            items: items.entries.map((e) => e.value).toList(),
            dropdownDecoratorProps: const DropDownDecoratorProps(
              dropdownSearchDecoration: InputDecoration(
                labelText: 'Select',
                border: OutlineInputBorder(),
              ),
            ),
            onChanged: (val) {
              selectedKey = items.entries.firstWhere((e) => e.value == val).key;
            },
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text("Cancel"),
            ),
            ElevatedButton(
              onPressed: () async {
                if (selectedKey != null) {
                  await onSelect(selectedKey!);
                  Navigator.pop(context);
                }
              },
              child: const Text("Assign"),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Padding(
        padding: const EdgeInsets.only(top: 8),
        child: ElevatedButton.icon(
          icon: const Icon(Icons.add),
          label: Text(label),
          onPressed: () => _openModal(context),
        ),
      ),
    );
  }
}

// Reusable dropdown + add button widget
class DropdownAssignWidget extends StatelessWidget {
  final String label;
  final Map<String, String> items;
  final String? selected;
  final void Function(String?) onChanged;
  final VoidCallback onAdd;

  const DropdownAssignWidget({
    required this.label,
    required this.items,
    required this.selected,
    required this.onChanged,
    required this.onAdd,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: DropdownSearch<String>(
            popupProps: PopupProps.menu(showSearchBox: true),
            dropdownDecoratorProps: DropDownDecoratorProps(
              dropdownSearchDecoration: InputDecoration(labelText: label),
            ),
            items: items.values.toList(),
            onChanged: (value) {
              final selectedKey = items.entries
                  .firstWhere((e) => e.value == value)
                  .key;
              onChanged(selectedKey);
            },
            selectedItem: selected != null ? items[selected] : null,
          ),
        ),
        IconButton(icon: const Icon(Icons.add), onPressed: onAdd),
      ],
    );
  }
}

class AddEditEmployeeScreen extends StatefulWidget {
  final Employee? employee;

  const AddEditEmployeeScreen({Key? key, this.employee}) : super(key: key);

  @override
  State<AddEditEmployeeScreen> createState() => _AddEditEmployeeScreenState();
}

class _AddEditEmployeeScreenState extends State<AddEditEmployeeScreen> {
  final _formKey = GlobalKey<FormState>();

  late String _name;
  late String _email;
  late String _phone;
  String _password = '';
  String _role = 'employee';

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  bool _isSaving = false;

  @override
  @override
  void initState() {
    super.initState();
    _name = widget.employee?.name ?? '';
    _email = widget.employee?.email ?? '';
    _phone = widget.employee?.phone ?? '';
    _role =
        widget.employee?.role.toLowerCase() ??
        'employee'; // normalize to lowercase
  }

  Future<void> _saveEmployee() async {
    if (!_formKey.currentState!.validate()) return;
    _formKey.currentState!.save();

    setState(() {
      _isSaving = true;
    });

    try {
      // Check for email or phone duplication
      final emailQuery = await _firestore
          .collection('users')
          .where('email', isEqualTo: _email)
          .get();

      final phoneQuery = await _firestore
          .collection('users')
          .where('phone', isEqualTo: _phone)
          .get();

      bool emailExists = emailQuery.docs.any(
        (doc) => widget.employee == null || doc.id != widget.employee!.id,
      );

      bool phoneExists = phoneQuery.docs.any(
        (doc) => widget.employee == null || doc.id != widget.employee!.id,
      );

      if (emailExists || phoneExists) {
        String message = '';
        if (emailExists) message += 'Email already exists. ';
        if (phoneExists) message += 'Phone number already exists.';
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(message)));
        setState(() => _isSaving = false);
        return;
      }

      if (widget.employee == null) {
        // ADD new employee (without Firebase Auth)
        await _firestore.collection('users').add({
          'name': _name,
          'email': _email,
          'phone': _phone,
          'role': _role,
          'org': 'own',
        });
      } else {
        // UPDATE existing employee
        final docRef = _firestore.collection('users').doc(widget.employee!.id);

        await docRef.update({
          'name': _name,
          'email': _email,
          'phone': _phone,
          'role': _role,
        });
      }

      Navigator.pop(context);
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text("Error saving employee: $e")));
    }

    setState(() {
      _isSaving = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.employee != null;

    return Scaffold(
      appBar: AppBar(title: Text(isEditing ? "Edit Employee" : "Add Employee")),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              TextFormField(
                initialValue: _name,
                decoration: const InputDecoration(labelText: "Name"),
                validator: (v) => v == null || v.isEmpty ? "Enter name" : null,
                onSaved: (v) => _name = v!.trim(),
              ),
              TextFormField(
                initialValue: _email,
                decoration: InputDecoration(labelText: 'Email'),
                enabled: widget.employee == null, // Disable if editing
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter email';
                  }
                  if (!value.contains('@')) {
                    return 'Enter a valid email';
                  }
                  return null;
                },
                onSaved: (value) => _email = value!.trim(),
              ),

              TextFormField(
                initialValue: _phone,
                decoration: const InputDecoration(labelText: "Phone"),
                validator: (v) => v == null || v.isEmpty ? "Enter phone" : null,
                onSaved: (v) => _phone = v!.trim(),
                keyboardType: TextInputType.phone,
              ),
              TextFormField(
                decoration: InputDecoration(
                  labelText: isEditing
                      ? "New Password (leave blank to keep)"
                      : "Password",
                ),
                obscureText: true,
                validator: (v) {
                  if (!isEditing && (v == null || v.isEmpty)) {
                    return "Enter password";
                  }
                  if (v != null && v.isNotEmpty && v.length < 6) {
                    return "Password must be at least 6 characters";
                  }
                  return null;
                },
                onSaved: (v) => _password = v ?? '',
              ),
              DropdownButtonFormField<String>(
                value: _role,
                items: const [
                  DropdownMenuItem(value: 'admin', child: Text('Admin')),
                  DropdownMenuItem(value: 'employee', child: Text('Employee')),
                ],
                onChanged: (v) => setState(() => _role = v ?? 'employee'),
                decoration: const InputDecoration(labelText: "Role"),
              ),

              const SizedBox(height: 20),
              _isSaving
                  ? const CircularProgressIndicator()
                  : ElevatedButton(
                      onPressed: _saveEmployee,
                      child: Text(isEditing ? "Save Changes" : "Add Employee"),
                    ),
            ],
          ),
        ),
      ),
    );
  }
}
