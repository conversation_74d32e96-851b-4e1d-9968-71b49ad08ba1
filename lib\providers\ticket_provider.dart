import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class TicketProvider with ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  List<Map<String, dynamic>> _tickets = [];
  List<Map<String, dynamic>> get tickets => _tickets;

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  /// Fetch tickets based on user role and organization
  Future<void> fetchTickets({
    required String role,
    required String org,
    required String email,
  }) async {
    _isLoading = true;
    notifyListeners();

    Query query = _firestore.collection('tickets');

    if (role == 'Client') {
      query = query.where('org', isEqualTo: org);
    } else if (role == 'Employee') {
      query = query.where('assignedTo', isEqualTo: email); // optional filter
    }

    final snapshot = await query.get();
    _tickets = snapshot.docs.map((doc) {
      final data = doc.data() as Map<String, dynamic>;
      data['id'] = doc.id; // include doc ID
      return data;
    }).toList();

    _isLoading = false;
    notifyListeners();
  }

  /// Create a new ticket with timestamp and createdBy
  Future<void> createTicket({
    required String service,
    required String org,
    required String problem,
    required String comments,
    required String workaround,
    required String priority,
    required String createdBy,
  }) async {
    final newTicket = {
      'service': service,
      'org': org,
      'problem': problem,
      'priority': priority,
      'comments': comments,
      'workaround': workaround,
      'createdBy': createdBy,
      'date': FieldValue.serverTimestamp(),
    };

    await _firestore.collection('tickets').add(newTicket);
    notifyListeners();
  }

  /// Update ticket (e.g. workaround, comments, priority)
  Future<void> updateTicket(
    String ticketId,
    Map<String, dynamic> updatedData,
  ) async {
    await _firestore.collection('tickets').doc(ticketId).update(updatedData);
    notifyListeners();
  }

  /// Delete ticket (use only for admin)
  Future<void> deleteTicket(String ticketId) async {
    await _firestore.collection('tickets').doc(ticketId).delete();
    _tickets.removeWhere((ticket) => ticket['id'] == ticketId);
    notifyListeners();
  }
}
