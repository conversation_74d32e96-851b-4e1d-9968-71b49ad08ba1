import 'package:flutter/material.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class AdminClientsScreen extends StatefulWidget {
  const AdminClientsScreen({Key? key}) : super(key: key);

  @override
  State<AdminClientsScreen> createState() => _AdminClientsScreenState();
}

class _AdminClientsScreenState extends State<AdminClientsScreen> {
  final _firestore = FirebaseFirestore.instance;

  void _openClientForm({DocumentSnapshot? clientDoc}) {
    final isEdit = clientDoc != null;
    final nameCtrl = TextEditingController(text: clientDoc?['name']);
    final scCtrl = TextEditingController(text: clientDoc?['shortcode']);
    final domainCtrl = TextEditingController(text: clientDoc?['domain']);
    final addrCtrl = TextEditingController(text: clientDoc?['address']);
    final userNameCtrl = TextEditingController();
    final userEmailCtrl = TextEditingController();
    final userPwdCtrl = TextEditingController();
    final userPhoneCtrl = TextEditingController();

    final formKey = GlobalKey<FormState>();
    final fieldErrors = <String, String?>{};

    // Use dialog for feedback so it's always visible
    Future<void> showMessage(String title, String msg, {bool success = true}) {
      return showDialog(
        context: context,
        builder: (_) => AlertDialog(
          title: Text(title),
          content: Text(msg),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK'),
            ),
          ],
        ),
      );
    }

    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (_) {
        return StatefulBuilder(
          builder: (context, setState) => Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
              top: 16,
              left: 16,
              right: 16,
            ),
            child: SingleChildScrollView(
              child: Form(
                key: formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      isEdit ? "Edit Client" : "Add Client",
                      style: const TextStyle(fontSize: 18),
                    ),
                    const SizedBox(height: 12),

                    TextFormField(
                      controller: nameCtrl,
                      decoration: InputDecoration(
                        labelText: 'Name',
                        errorText: fieldErrors['name'],
                      ),
                    ),
                    const SizedBox(height: 20),

                    TextFormField(
                      controller: scCtrl,
                      decoration: InputDecoration(
                        labelText: 'Shortcode',
                        errorText: fieldErrors['shortcode'],
                      ),
                      enabled: !isEdit,
                    ),
                    const SizedBox(height: 20),

                    TextFormField(
                      controller: domainCtrl,
                      decoration: InputDecoration(
                        labelText: 'Domain',
                        errorText: fieldErrors['domain'],
                      ),
                    ),
                    const SizedBox(height: 20),

                    TextFormField(
                      controller: addrCtrl,
                      decoration: InputDecoration(
                        labelText: 'Address',
                        errorText: fieldErrors['address'],
                      ),
                    ),

                    if (!isEdit) ...[
                      const SizedBox(height: 16),
                      const Text(
                        "First Client User",
                        style: TextStyle(fontSize: 16),
                      ),
                      const SizedBox(height: 8),

                      TextFormField(
                        controller: userNameCtrl,
                        decoration: InputDecoration(
                          labelText: 'User Name',
                          errorText: fieldErrors['username'],
                        ),
                      ),
                      const SizedBox(height: 8),

                      TextFormField(
                        controller: userEmailCtrl,
                        decoration: InputDecoration(
                          labelText: 'User Email',
                          errorText: fieldErrors['email'],
                        ),
                      ),
                      const SizedBox(height: 8),

                      TextFormField(
                        controller: userPwdCtrl,
                        decoration: InputDecoration(
                          labelText: 'Password',
                          errorText: fieldErrors['password'],
                        ),
                        obscureText: true,
                      ),
                      const SizedBox(height: 8),

                      TextFormField(
                        controller: userPhoneCtrl,
                        decoration: InputDecoration(
                          labelText: 'Phone',
                          errorText: fieldErrors['phone'],
                        ),
                      ),
                    ],

                    const SizedBox(height: 20),
                    ElevatedButton(
                      child: Text(
                        isEdit ? "Save Client" : "Create Client & User",
                      ),
                      onPressed: () async {
                        fieldErrors.clear();
                        final name = nameCtrl.text.trim();
                        final sc = scCtrl.text.trim();
                        final domain = domainCtrl.text.trim();
                        final addr = addrCtrl.text.trim();

                        if (name.isEmpty) fieldErrors['name'] = 'Required';
                        if (sc.isEmpty) fieldErrors['shortcode'] = 'Required';
                        if (domain.isEmpty) fieldErrors['domain'] = 'Required';
                        if (addr.isEmpty) fieldErrors['address'] = 'Required';

                        if (!isEdit) {
                          final uname = userNameCtrl.text.trim();
                          final uemail = userEmailCtrl.text.trim();
                          final upwd = userPwdCtrl.text.trim();
                          final uphone = userPhoneCtrl.text.trim();

                          if (uname.isEmpty)
                            fieldErrors['username'] = 'Required';
                          if (uemail.isEmpty) fieldErrors['email'] = 'Required';
                          if (upwd.isEmpty)
                            fieldErrors['password'] = 'Required';
                          if (uphone.isEmpty) fieldErrors['phone'] = 'Required';
                        }

                        if (fieldErrors.isNotEmpty) {
                          setState(() {}); // 👈 Correct way to update UI
                          await showMessage(
                            'Validation Error',
                            'Please fix all highlighted fields.',
                            success: false,
                          );
                          return;
                        }

                        final coll = _firestore.collection('clients');

                        try {
                          if (!isEdit) {
                            final scCheck = await coll
                                .where('shortcode', isEqualTo: sc)
                                .get();
                            if (scCheck.docs.isNotEmpty) {
                              fieldErrors['shortcode'] =
                                  'Shortcode already exists';
                              setState(() {});
                              await showMessage(
                                'Duplicate Shortcode',
                                'Shortcode already exists.',
                                success: false,
                              );
                              return;
                            }

                            final userColl = _firestore.collection('users');
                            final uemail = userEmailCtrl.text.trim();
                            final uphone = userPhoneCtrl.text.trim();

                            final emailExists = await userColl
                                .where('email', isEqualTo: uemail)
                                .get();
                            if (emailExists.docs.isNotEmpty) {
                              fieldErrors['email'] = 'Email already exists';
                              setState(() {});
                              await showMessage(
                                'Duplicate Email',
                                'The user email is already registered.',
                                success: false,
                              );
                              return;
                            }

                            final phoneExists = await userColl
                                .where('phone', isEqualTo: uphone)
                                .get();
                            if (phoneExists.docs.isNotEmpty) {
                              fieldErrors['phone'] = 'Phone already exists';
                              setState(() {});
                              await showMessage(
                                'Duplicate Phone',
                                'The phone number is already registered.',
                                success: false,
                              );
                              return;
                            }

                            // Insert client and user
                            await coll.add({
                              'name': name,
                              'shortcode': sc,
                              'domain': domain,
                              'address': addr,
                            });
                            await userColl.add({
                              'name': userNameCtrl.text.trim(),
                              'email': uemail,
                              'pwd': userPwdCtrl.text.trim(),
                              'phone': uphone,
                              'org': sc,
                              'role': 'Client',
                            });

                            Navigator.pop(context);
                            await showMessage(
                              'Created',
                              'Client and user created.',
                              success: true,
                            );
                          } else {
                            await coll.doc(clientDoc!.id).update({
                              'name': name,
                              'domain': domain,
                              'address': addr,
                            });
                            Navigator.pop(context);
                            await showMessage(
                              'Updated',
                              'Client updated successfully.',
                              success: true,
                            );
                          }
                        } catch (e) {
                          await showMessage(
                            'Error',
                            'Operation failed: $e',
                            success: false,
                          );
                        }
                      },
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Clients')),
      body: StreamBuilder<QuerySnapshot>(
        stream: FirebaseFirestore.instance.collection('clients').snapshots(),
        builder: (ctx, snap) {
          if (!snap.hasData) {
            return const Center(child: CircularProgressIndicator());
          }
          final docs = snap.data!.docs;

          if (docs.isEmpty) {
            return const Center(child: Text("No clients found."));
          }

          return ListView.builder(
            padding: const EdgeInsets.all(12),
            itemCount: docs.length,
            itemBuilder: (ctx, i) {
              final d = docs[i];
              final name = d['name'] ?? 'Unnamed';
              final shortcode = d['shortcode'] ?? 'N/A';

              return Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                margin: const EdgeInsets.symmetric(vertical: 6),
                child: ListTile(
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 10,
                  ),
                  leading: const CircleAvatar(
                    backgroundColor: Colors.blueAccent,
                    child: Icon(Icons.business, color: Colors.white),
                  ),
                  title: Text(
                    name,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  subtitle: Text(
                    'Shortcode: $shortcode',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(fontSize: 13, color: Colors.grey),
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (_) => ClientDetailScreen(client: d),
                    ),
                  ),
                ),
              );
            },
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _openClientForm(),
        child: const Icon(Icons.add),
        tooltip: 'Add Client',
      ),
    );
  }
}

// *************** Client Detail Page ***************

class ClientDetailScreen extends StatefulWidget {
  final DocumentSnapshot client;

  const ClientDetailScreen({required this.client, Key? key}) : super(key: key);

  @override
  State<ClientDetailScreen> createState() => _ClientDetailScreenState();
}

class _ClientDetailScreenState extends State<ClientDetailScreen> {
  List<DocumentSnapshot> clientUsers = [];
  List<Map<String, dynamic>> clientServices = [];
  List<Map<String, dynamic>> allServices = [];
  bool isLoadingUsers = true;
  bool isLoadingServices = true;

  bool isEditingClient = false;

  late TextEditingController nameController;
  late TextEditingController domainController;
  late TextEditingController addressController;

  @override
  void initState() {
    super.initState();
    fetchClientUsers();
    fetchClientServices();
    fetchAllServices();

    nameController = TextEditingController(text: widget.client['name']);
    domainController = TextEditingController(text: widget.client['domain']);
    addressController = TextEditingController(text: widget.client['address']);
  }

  Future<void> fetchClientUsers() async {
    final snapshot = await FirebaseFirestore.instance
        .collection('users')
        .where('org', isEqualTo: widget.client['shortcode'])
        .where('role', isEqualTo: 'Client')
        .get();

    setState(() {
      clientUsers = snapshot.docs;
      isLoadingUsers = false;
    });
  }

  Future<void> fetchClientServices() async {
    final snapshot = await FirebaseFirestore.instance
        .collection('cltosrv')
        .where('clshortcode', isEqualTo: widget.client['shortcode'])
        .get();

    final serviceShortcodes = snapshot.docs
        .map((doc) => doc['srvshortcode'])
        .toList();

    if (serviceShortcodes.isEmpty) {
      setState(() {
        clientServices = [];
        isLoadingServices = false;
      });
      return;
    }

    final servicesSnapshot = await FirebaseFirestore.instance
        .collection('services')
        .where('shortcode', whereIn: serviceShortcodes)
        .get();

    setState(() {
      clientServices = servicesSnapshot.docs
          .map(
            (doc) => {
              'id': doc.id,
              'service': doc['service'],
              'type': doc['type'],
              'shortcode': doc['shortcode'],
            },
          )
          .toList();
      isLoadingServices = false;
    });
  }

  Widget buildClientField(String label, TextEditingController controller) {
    return TextFormField(
      controller: controller,
      decoration: const InputDecoration(
        border: UnderlineInputBorder(),
        isDense: true,
      ),
      style: const TextStyle(fontSize: 16),
    );
  }

  Widget buildReadOnlyField(String label, String value) {
    return Text(value, style: const TextStyle(fontSize: 16));
  }

  Future<void> fetchAllServices() async {
    final snapshot = await FirebaseFirestore.instance
        .collection('services')
        .get();
    setState(() {
      allServices = snapshot.docs.map((doc) {
        final data = doc.data();
        return {
          'shortcode': data['shortcode'],
          'service': data['service'],
          'type': data['type'],
        };
      }).toList();
    });
  }

  void openUserModal({DocumentSnapshot? user}) {
    final _formKey = GlobalKey<FormState>();
    final data = user?.data() as Map<String, dynamic>?;

    final nameCtrl = TextEditingController(text: data?['name'] ?? '');
    final emailCtrl = TextEditingController(text: data?['email'] ?? '');
    final phoneCtrl = TextEditingController(text: data?['phone'] ?? '');
    final passCtrl = TextEditingController(text: data?['password'] ?? '');

    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: Text(user == null ? 'Add Client User' : 'Edit Client User'),
        content: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: nameCtrl,
                decoration: InputDecoration(labelText: 'Name'),
                validator: (v) => v!.isEmpty ? 'Enter name' : null,
              ),
              TextFormField(
                controller: emailCtrl,
                decoration: InputDecoration(labelText: 'Email'),
                validator: (v) => v!.isEmpty ? 'Enter email' : null,
              ),
              TextFormField(
                controller: phoneCtrl,
                decoration: InputDecoration(labelText: 'Phone'),
              ),
              TextFormField(
                controller: passCtrl,
                decoration: InputDecoration(labelText: 'Password'),
                obscureText: true,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (_formKey.currentState!.validate()) {
                final map = {
                  'name': nameCtrl.text,
                  'email': emailCtrl.text,
                  'phone': phoneCtrl.text,
                  'password': passCtrl.text,
                  'role': 'Client',
                  'org': widget.client['shortcode'],
                };

                if (user == null) {
                  await FirebaseFirestore.instance.collection('users').add(map);
                } else {
                  await FirebaseFirestore.instance
                      .collection('users')
                      .doc(user.id)
                      .update(map);
                }

                Navigator.pop(context);
                fetchClientUsers();
              }
            },
            child: Text('Save'),
          ),
        ],
      ),
    );
  }

  void openServiceModal() {
    final _formKey = GlobalKey<FormState>();
    String? selectedShortcode;

    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: Text('Assign Service'),
        content: Form(
          key: _formKey,
          child: DropdownButtonFormField<String>(
            isExpanded: true,
            decoration: InputDecoration(labelText: 'Select Service'),
            items: allServices.map((srv) {
              return DropdownMenuItem<String>(
                value: srv['shortcode'],
                child: Text(srv['service']),
              );
            }).toList(),
            onChanged: (val) => selectedShortcode = val,
            validator: (val) => val == null ? 'Select a service' : null,
          ),
        ),
        actions: [
          TextButton(
            child: Text('Cancel'),
            onPressed: () => Navigator.pop(context),
          ),
          ElevatedButton(
            child: Text('Assign'),
            onPressed: () async {
              if (_formKey.currentState!.validate()) {
                await FirebaseFirestore.instance.collection('cltosrv').add({
                  'clshortcode': widget.client['shortcode'],
                  'srvshortcode': selectedShortcode,
                });
                Navigator.pop(context);
                fetchClientServices();
              }
            },
          ),
        ],
      ),
    );
  }

  void removeService(String shortcode) async {
    final doc = await FirebaseFirestore.instance
        .collection('cltosrv')
        .where('clshortcode', isEqualTo: widget.client['shortcode'])
        .where('srvshortcode', isEqualTo: shortcode)
        .limit(1)
        .get();

    if (doc.docs.isNotEmpty) {
      await doc.docs.first.reference.delete();
      fetchClientServices();
    }
  }

  Future<void> saveClientDetails() async {
    await FirebaseFirestore.instance
        .collection('clients')
        .doc(widget.client.id)
        .update({
          'name': nameController.text,
          'domain': domainController.text,
          'address': addressController.text,
        });

    setState(() => isEditingClient = false);
  }

  Widget buildUserSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Client Users',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        Divider(),
        Expanded(
          child: isLoadingUsers
              ? Center(child: CircularProgressIndicator())
              : clientUsers.isEmpty
              ? Center(child: Text('No users found'))
              : ListView.builder(
                  itemCount: clientUsers.length,
                  itemBuilder: (ctx, i) {
                    final user = clientUsers[i];
                    final data = user.data() as Map<String, dynamic>;
                    return Card(
                      elevation: 2,
                      margin: const EdgeInsets.symmetric(
                        vertical: 4,
                        horizontal: 2,
                      ),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: Colors.blueAccent,
                          child: Icon(Icons.person, color: Colors.white),
                        ),
                        title: Text(
                          data['name'],
                          style: TextStyle(fontWeight: FontWeight.w600),
                        ),
                        subtitle: Text(
                          '${data['email']} | ${data['phone']}',
                          style: TextStyle(fontSize: 12),
                        ),
                        trailing: IconButton(
                          icon: Icon(Icons.edit, color: Colors.blueAccent),
                          onPressed: () => openUserModal(user: user),
                        ),
                      ),
                    );
                  },
                ),
        ),
        Center(
          child: ElevatedButton.icon(
            icon: Icon(Icons.add),
            label: Text('Add User'),
            onPressed: () => openUserModal(),
          ),
        ),
      ],
    );
  }

  Widget buildServiceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Client Services',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        Divider(),
        Expanded(
          child: isLoadingServices
              ? Center(child: CircularProgressIndicator())
              : clientServices.isEmpty
              ? Center(child: Text('No services found'))
              : ListView.builder(
                  itemCount: clientServices.length,
                  itemBuilder: (ctx, i) {
                    final srv = clientServices[i];
                    return Card(
                      elevation: 2,
                      margin: const EdgeInsets.symmetric(
                        vertical: 4,
                        horizontal: 2,
                      ),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: Colors.green,
                          child: Icon(
                            Icons.miscellaneous_services,
                            color: Colors.white,
                          ),
                        ),
                        title: Text(
                          srv['service'],
                          style: TextStyle(fontWeight: FontWeight.w600),
                        ),
                        subtitle: Text(
                          srv['type'],
                          style: TextStyle(fontSize: 12),
                        ),
                        trailing: IconButton(
                          icon: Icon(Icons.delete, color: Colors.redAccent),
                          onPressed: () => removeService(srv['shortcode']),
                        ),
                      ),
                    );
                  },
                ),
        ),
        Center(
          child: ElevatedButton.icon(
            icon: Icon(Icons.add),
            label: Text('Assign Service'),
            onPressed: () => openServiceModal(),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = MediaQuery.of(context).size.width < 600;

    return Scaffold(
      appBar: AppBar(
        title: Text('Client Details'),
        actions: [
          IconButton(
            icon: Icon(isEditingClient ? Icons.save : Icons.edit),
            onPressed: () {
              if (isEditingClient) {
                saveClientDetails();
              } else {
                setState(() => isEditingClient = true);
              }
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            // Client Info Fields
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    _buildFieldWithIcon(
                      icon: Icons.business,
                      child: isEditingClient
                          ? buildClientField('Client Name', nameController)
                          : buildReadOnlyField(
                              'Client Name',
                              widget.client['name'],
                            ),
                    ),
                    const Divider(height: 20),
                    _buildFieldWithIcon(
                      icon: Icons.language,
                      child: isEditingClient
                          ? buildClientField('Domain', domainController)
                          : buildReadOnlyField(
                              'Domain',
                              widget.client['domain'],
                            ),
                    ),
                    const Divider(height: 20),
                    _buildFieldWithIcon(
                      icon: Icons.location_on,
                      child: isEditingClient
                          ? buildClientField('Address', addressController)
                          : buildReadOnlyField(
                              'Address',
                              widget.client['address'],
                            ),
                    ),
                    const Divider(height: 20),
                    _buildFieldWithIcon(
                      icon: Icons.qr_code,
                      child: buildReadOnlyField(
                        'Shortcode',
                        widget.client['shortcode'],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            SizedBox(height: 12),

            // Users and Services Section
            Expanded(
              child: isSmallScreen
                  ? Column(
                      children: [
                        Expanded(child: buildUserSection()),
                        SizedBox(height: 12),
                        Expanded(child: buildServiceSection()),
                      ],
                    )
                  : Row(
                      children: [
                        Expanded(child: buildUserSection()),
                        VerticalDivider(width: 20),
                        Expanded(child: buildServiceSection()),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFieldWithIcon({required IconData icon, required Widget child}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 20, color: Colors.blue),
        const SizedBox(width: 12),
        Expanded(child: child),
      ],
    );
  }
}
