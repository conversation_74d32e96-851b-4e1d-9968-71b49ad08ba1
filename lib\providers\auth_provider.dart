import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class AuthProvider with ChangeNotifier {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  User? _firebaseUser;
  Map<String, dynamic>? _userData;

  User? get firebaseUser => _firebaseUser;
  Map<String, dynamic>? get userData => _userData;
  String? get userRole => _userData?['role'];

  bool get isAuthenticated => _firebaseUser != null;

  /// Called on app launch to check user state
  Future<void> checkLoginStatus() async {
    _firebaseUser = _auth.currentUser;

    if (_firebaseUser != null) {
      await fetchUserData();
    }

    notifyListeners();
  }

  /// Login using email and password
  Future<String?> login(String email, String password) async {
    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      _firebaseUser = credential.user;

      await fetchUserData();
      notifyListeners();
      return null; // No error
    } on FirebaseAuthException catch (e) {
      return e.message;
    }
  }

  /// Fetch user data from 'users' collection in Firestore
  Future<void> fetchUserData() async {
    if (_firebaseUser == null) return;

    final doc = await _firestore.collection('users').doc(_firebaseUser!.uid).get();
    if (doc.exists) {
      _userData = doc.data();
    } else {
      _userData = null;
    }
  }

  /// Logout and clear user state
  Future<void> logout() async {
    await _auth.signOut();
    _firebaseUser = null;
    _userData = null;
    notifyListeners();
  }

  /// Get redirect route based on role
  String getRedirectRoute() {
    switch (userRole) {
      case 'Admin':
        return '/admin/dashboard';
      case 'Employee':
        return '/employee/dashboard';
      case 'Client':
        return '/client/dashboard';
      default:
        return '/login';
    }
  }
}
