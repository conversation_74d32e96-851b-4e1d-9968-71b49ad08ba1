import 'package:flutter/material.dart';
import '../screens/login/login_screen.dart';
import '../screens/admin/dashboard_screen.dart';
import '../screens/employee/dashboard_screen.dart';
import '../screens/client/dashboard_screen.dart';
import '../widgets/bottom_navbar.dart'; // 👈 required for RoleBasedBottomNavBar

class AppRouter {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      /*
      case '/':
        // ✅ TEMPORARY: Directly go to Admin BottomNavBar with hardcoded test email
        return MaterialPageRoute(
          builder: (_) => const RoleBasedBottomNavBar(
            role: 'Client',
            email: '<EMAIL>',
            org: 'AKUH',
          ),
        );

      // ❌ ORIGINAL LOGIN SCREEN (Commented out for testing)
      // return MaterialPageRoute(builder: (_) => const LoginScreen());
      */
      case '/':
        return MaterialPageRoute(builder: (_) => const LoginScreen());
      case '/login':
        return MaterialPageRoute(builder: (_) => const LoginScreen());

      case '/home': // This will be the route after successful login
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (_) => RoleBasedBottomNavBar(
            role: args['role'],
            email: args['email'],
            org: args['org'],
          ),
        );
      case '/adminDashboard':
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (_) => AdminDashboardScreen(email: args['email']),
        );
      case '/employeeDashboard':
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (_) => EmployeeDashboardScreen(email: args['email']),
        );

      case '/clientDashboard':
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (_) =>
              ClientDashboardScreen(email: args['email'], org: args['org']),
        );

      default:
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            body: Center(child: Text('No route defined for ${settings.name}')),
          ),
        );
    }
  }
}
