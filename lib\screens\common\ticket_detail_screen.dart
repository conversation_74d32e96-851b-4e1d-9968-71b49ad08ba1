import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';

class TicketDetailScreen extends StatefulWidget {
  final Map<String, dynamic> ticket;
  final Map<String, String> orgNames;
  final Map<String, String> serviceNames;
  final String role;
  final String currentUserEmail;

  const TicketDetailScreen({
    Key? key,
    required this.role,
    required this.ticket,
    required this.orgNames,
    required this.serviceNames,
    required this.currentUserEmail,
  }) : super(key: key);

  @override
  State<TicketDetailScreen> createState() => _TicketDetailScreenState();
}

class _TicketDetailScreenState extends State<TicketDetailScreen> {
  final TextEditingController chatController = TextEditingController();
  final TextEditingController commentsController = TextEditingController();
  final _firestore = FirebaseFirestore.instance;
  bool isClosed = false;

  @override
  void initState() {
    super.initState();
    isClosed = widget.ticket['status'] == 'Closed';
  }

  Future<void> _closeTicket() async {
    debugPrint(widget.ticket.toString());
    if (commentsController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Closing comments are required')),
      );
      return;
    }

    try {
      // Update ticket status
      final query = await FirebaseFirestore.instance
          .collection('tickets')
          .where('ticketid', isEqualTo: widget.ticket['ticketid'])
          .get();

      for (var doc in query.docs) {
        await doc.reference.update({'status': 'Closed'});
      }

      // Create status audit record
      await _firestore.collection('statusaud').add({
        'ticketid': widget.ticket['ticketid'],
        'oldstatus': widget.ticket['status'],
        'newstatus': 'Closed',
        'comments': commentsController.text,
        'updatedby': widget.currentUserEmail,
        'date': FieldValue.serverTimestamp(),
      });

      setState(() {
        isClosed = true;
        widget.ticket['status'] = 'Closed';
      });

      Navigator.of(context).pop(); // Close the dialog
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Ticket closed successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error closing ticket: $e')));
    }
  }

  void _showCloseTicketDialog() {
    final serviceFull =
        widget.serviceNames[widget.ticket['service']] ??
        widget.ticket['service'] ??
        'N/A';
    final orgFull =
        widget.orgNames[widget.ticket['org']] ?? widget.ticket['org'] ?? 'N/A';

    showDialog(
      context: context,
      barrierDismissible: false, // Prevent closing by tapping outside
      builder: (context) => AlertDialog(
        title: Text('Close Ticket #${widget.ticket['ticketid']}?'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Organization: $orgFull\nService: $serviceFull'),
            const SizedBox(height: 16),
            const Text('Closing comments (required):'),
            const SizedBox(height: 8),
            TextFormField(
              controller: commentsController,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'Enter closing comments...',
              ),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter closing comments';
                }
                return null;
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('NO'),
          ),
          ElevatedButton(onPressed: _closeTicket, child: const Text('YES')),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final serviceFull =
        widget.serviceNames[widget.ticket['service']] ??
        widget.ticket['service'] ??
        'N/A';
    final orgFull =
        widget.orgNames[widget.ticket['org']] ?? widget.ticket['org'] ?? 'N/A';
    final createdDate = (widget.ticket['date'] as Timestamp?)?.toDate();
    final formattedDate = createdDate != null
        ? DateFormat.yMMMd().add_jm().format(createdDate)
        : 'No Date';

    return Scaffold(
      appBar: AppBar(
        title: Text('Ticket #${widget.ticket['ticketid'] ?? 'N/A'}'),
        actions: [
          if (widget.role != 'Client' && !isClosed)
            PopupMenuButton(
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'close',
                  child: Text('Close Ticket'),
                ),
              ],
              onSelected: (value) {
                if (value == 'close') {
                  _showCloseTicketDialog();
                }
              },
            ),
        ],
      ),
      body: Column(
        children: [
          // 📄 Ticket Summary Card
          Padding(
            padding: const EdgeInsets.all(16),
            child: Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              elevation: 4,
              color: isClosed ? Colors.green.shade50 : null,
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (isClosed)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: Row(
                          children: [
                            Icon(
                              Icons.check_circle,
                              color: Colors.green.shade700,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'CLOSED TICKET',
                              style: TextStyle(
                                color: Colors.green.shade700,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    _detailRow('Service', serviceFull),
                    const SizedBox(height: 8),
                    _detailRow('Organization', orgFull),
                    const SizedBox(height: 8),
                    _detailRow('Priority', widget.ticket['priority'] ?? 'N/A'),
                    const SizedBox(height: 8),
                    _detailRow('Status', widget.ticket['status'] ?? 'Open'),
                    const SizedBox(height: 8),
                    _detailRow('Created On', formattedDate),
                    const SizedBox(height: 12),
                    const Text(
                      'Problem Description',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      widget.ticket['problem'] ?? 'No details provided',
                      style: const TextStyle(fontSize: 14),
                    ),
                    const SizedBox(height: 12),
                    if (widget.ticket['workaround']?.isNotEmpty ?? false) ...[
                      const Text(
                        'Workaround',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 6),
                      Text(
                        widget.ticket['workaround'] ?? '',
                        style: const TextStyle(fontSize: 14),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),

          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'Chat',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
          ),

          // 💬 Chat Box (always visible)
          Expanded(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 12),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: const Center(
                child: Text(
                  'Messages will appear here...',
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            ),
          ),

          // 📨 Chat Input Area (disabled if closed)
          Padding(
            padding: const EdgeInsets.fromLTRB(12, 8, 12, 12),
            child: Row(
              children: [
                // 📎 Attachment Button
                IconButton(
                  icon: const Icon(Icons.attach_file),
                  onPressed: isClosed
                      ? null
                      : () {
                          // TODO: Add attachment logic
                        },
                ),

                // ✍️ Message TextField (read-only if closed)
                Expanded(
                  child: TextField(
                    controller: chatController,
                    readOnly: isClosed,
                    decoration: InputDecoration(
                      hintText: isClosed
                          ? 'Chat disabled for closed ticket'
                          : 'Type your message...',
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      filled: true,
                      fillColor: Colors.white,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                        borderSide: BorderSide(color: Colors.grey.shade300),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                        borderSide: BorderSide(color: Colors.grey.shade300),
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                // 📤 Send Button
                CircleAvatar(
                  backgroundColor: isClosed
                      ? Colors.grey
                      : Theme.of(context).primaryColor,
                  child: IconButton(
                    icon: const Icon(Icons.send, color: Colors.white),
                    onPressed: isClosed
                        ? null
                        : () {
                            final text = chatController.text.trim();
                            if (text.isNotEmpty) {
                              // TODO: Implement send logic
                              chatController.clear();
                            }
                          },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Helper widget to display a row with title and value
  Widget _detailRow(String title, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            '$title:',
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 15),
          ),
        ),
        Expanded(child: Text(value, style: const TextStyle(fontSize: 15))),
      ],
    );
  }
}
