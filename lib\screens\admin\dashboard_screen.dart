// same imports
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';

class AdminDashboardScreen extends StatefulWidget {
  final String email;
  const AdminDashboardScreen({super.key, required this.email});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  String selectedField = 'No Filter';
  String? selectedValue;

  final Map<String, String> orgNames = {};
  final Map<String, String> serviceNames = {};

  int totalTickets = 0;
  int openTickets = 0;
  int closedTickets = 0;
  int totalClients = 0;
  int totalServices = 0;
  int totalUsers = 0;
  int totalEmployees = 0;

  List<Map<String, dynamic>> recentTickets = [];

  @override
  void initState() {
    super.initState();
    Future.microtask(() => preloadNames());
  }

  Future<void> preloadNames() async {
    final results = await Future.wait([
      _firestore.collection('clients').get(),
      _firestore.collection('services').get(),
      _firestore.collection('users').get(),
    ]);

    final clientsSnap = results[0];
    for (var doc in clientsSnap.docs) {
      orgNames[doc['shortcode']] = doc['name'];
    }

    final servicesSnap = results[1];
    for (var doc in servicesSnap.docs) {
      serviceNames[doc['shortcode']] = doc['service'];
    }

    final usersSnap = results[2];
    totalUsers = usersSnap.docs.length;

    totalClients = orgNames.length;
    totalServices = serviceNames.length;
    await _loadStats();
  }

  Future<void> _loadStats() async {
    Query ticketsQuery = _firestore.collection('tickets');

    // Apply filter if selected
    if (selectedField != 'No Filter' && selectedValue != null) {
      final field = selectedField == 'Organization'
          ? 'org'
          : selectedField == 'Service'
          ? 'service'
          : null;

      if (field != null) {
        ticketsQuery = ticketsQuery.where(field, isEqualTo: selectedValue);
      }
    }
    debugPrint(selectedValue);
    // Fetch all tickets matching filter for stats
    final snap = await ticketsQuery.get();
    final allTickets = snap.docs;

    totalTickets = allTickets.length;
    openTickets = allTickets.where((t) => t['status'] == 'Open').length;
    closedTickets = allTickets.where((t) => t['status'] == 'Closed').length;

    // Reuse the same query, but now for recent tickets (with order + limit)
    final recentSnap = await ticketsQuery
        .orderBy('date', descending: true)
        .limit(3)
        .get();

    recentTickets = recentSnap.docs
        .map((d) => d.data() as Map<String, dynamic>)
        .toList();

    await _loadFilteredEmployees();

    setState(() {});
  }

  Future<void> _loadFilteredEmployees() async {
    Set<String> assignedEmails = {};
    Set<String> userEmails = {};

    final usersSnap = await _firestore.collection('users').get();
    final usersData = usersSnap.docs.map((d) => d.data()).toList();

    if (selectedField == 'Organization' && selectedValue != null) {
      // Employees assigned to this org
      final empToClSnap = await _firestore
          .collection('emptocl')
          .where('clshortcode', isEqualTo: selectedValue)
          .get();
      assignedEmails = empToClSnap.docs
          .map((d) => d['empemail'] as String)
          .toSet();

      // Users with org = selectedValue and role = Client
      userEmails = usersData
          .where(
            (u) =>
                u['role'] == 'Client' &&
                u['org'] != null &&
                u['org'] == selectedValue,
          )
          .map((u) => u['email'] as String)
          .toSet();
    } else if (selectedField == 'Service' && selectedValue != null) {
      // 1. Get clients using this service
      final clToSrvSnap = await _firestore
          .collection('cltosrv')
          .where('srvshortcode', isEqualTo: selectedValue)
          .get();
      final orgShortcodes = clToSrvSnap.docs
          .map((d) => d['clshortcode'] as String)
          .toSet();

      // 2. Get employees mapped to these clients
      final empToSrvSnap = await _firestore
          .collection('emptosrv')
          .where('srvshortcode', isEqualTo: selectedValue)
          .get();
      assignedEmails = empToSrvSnap.docs
          .map((d) => d['empemail'] as String)
          .toSet();
      //debugPrint();
      // 3. Get users with matching org and role = Client
      userEmails = usersData
          .where(
            (u) =>
                u['role'] == 'Client' &&
                u['org'] != null &&
                orgShortcodes.contains(u['org']),
          )
          .map((u) => u['email'] as String)
          .toSet();
    } else {
      // No filter: combine all emails from emptocl & emptosrv
      final results = await Future.wait([
        _firestore.collection('emptocl').get(),
        _firestore.collection('emptosrv').get(),
      ]);

      final allAssign1 = results[0];
      final allAssign2 = results[1];

      assignedEmails = {
        ...allAssign1.docs.map((d) => d['empemail'] as String),
        ...allAssign2.docs.map((d) => d['empemail'] as String),
      };

      userEmails = usersData
          .where((u) => u['role'] == 'Client')
          .map((u) => u['email'] as String)
          .toSet();
    }

    final employeeEmails = usersData
        .where(
          (u) =>
              (u['org'] == 'own' || u['role'] != 'Client') &&
              assignedEmails.contains(u['email']),
        )
        .map((u) => u['email'] as String)
        .toSet();

    totalEmployees = employeeEmails.length;
    totalUsers = userEmails.length;
    if (selectedField == 'No Filter' || selectedValue == null) {
      // SELECT COUNT(name) FROM clients
      totalClients = orgNames.length;
    } else if (selectedField == 'Service' && selectedValue != null) {
      // SELECT COUNT(name) FROM clients c
      // JOIN cltosrv cs ON c.shortcode = cs.clshortcode
      // WHERE cs.srvshortcode = selectedValue
      final clToSrvSnap = await _firestore
          .collection('cltosrv')
          .where('srvshortcode', isEqualTo: selectedValue)
          .get();

      final clientCodes = clToSrvSnap.docs
          .map((d) => d['clshortcode'] as String)
          .toSet();
      totalClients = clientCodes.length;
    } else if (selectedField == 'Organization' && selectedValue != null) {
      // SELECT COUNT(name) FROM services WHERE shortcode = selectedValue
      final servicesSnap = await _firestore
          .collection('clients')
          .where('shortcode', isEqualTo: selectedValue)
          .get();
      totalClients = servicesSnap.docs.length;
    }
    if (selectedField == 'No Filter' || selectedValue == null) {
      // SELECT COUNT(service) FROM services
      totalServices = serviceNames.length;
    } else if (selectedField == 'Organization' && selectedValue != null) {
      // SELECT COUNT(s.service) FROM services s
      // JOIN cltosrv cs ON s.shortcode = cs.srvshortcode
      // WHERE cs.clshortcode = selectedValue
      final clToSrvSnap = await _firestore
          .collection('cltosrv')
          .where('clshortcode', isEqualTo: selectedValue)
          .get();

      final serviceCodes = clToSrvSnap.docs
          .map((d) => d['srvshortcode'] as String)
          .toSet();
      totalServices = serviceCodes.length;
    } else if (selectedField == 'Service' && selectedValue != null) {
      // SELECT COUNT(service) FROM services WHERE shortcode = selectedValue
      final servicesSnap = await _firestore
          .collection('services')
          .where('shortcode', isEqualTo: selectedValue)
          .get();
      totalServices = servicesSnap.docs.length;
    }
  }

  Widget _buildResponsiveStats(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double screenWidth = constraints.maxWidth;
        int cardsPerRow = 3;
        if (screenWidth >= 900) {
          cardsPerRow = 5;
        } else if (screenWidth >= 600) {
          cardsPerRow = 4;
        }

        double spacing = 12;
        double cardWidth =
            (screenWidth - spacing * (cardsPerRow - 1)) / cardsPerRow;

        List<Widget> cards = [
          _buildStatCard(
            'Total \nTickets',
            totalTickets,
            Colors.blue,
            cardWidth,
          ),
          _buildStatCard(
            'Open \nTickets',
            openTickets,
            Colors.orange,
            cardWidth,
          ),
          _buildStatCard(
            'Closed \nTickets',
            closedTickets,
            Colors.green,
            cardWidth,
          ),
          _buildStatCard(
            'Total \nClients',
            totalClients,
            Colors.purple,
            cardWidth,
          ),
          _buildStatCard(
            'Total \nServices',
            totalServices,
            Colors.teal,
            cardWidth,
          ),
          _buildStatCard('Total \nUsers', totalUsers, Colors.indigo, cardWidth),
          _buildStatCard(
            'Total \nEmployees',
            totalEmployees,
            Colors.brown,
            cardWidth,
          ),
        ];

        return Wrap(spacing: spacing, runSpacing: spacing, children: cards);
      },
    );
  }

  Widget _buildStatCard(String title, int count, Color color, double width) {
    return SizedBox(
      width: width,
      child: Card(
        color: color,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 10),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircleAvatar(
                radius: 28,
                backgroundColor: Colors.white,
                child: Text(
                  '$count',
                  style: TextStyle(
                    color: color,
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                  ),
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(color: Colors.white, fontSize: 14),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final List<String> dropdownOptions = selectedField == 'Organization'
        ? orgNames.values.toList()
        : selectedField == 'Service'
        ? serviceNames.values.toList()
        : [];

    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () => Navigator.pushReplacementNamed(context, '/'),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Filters
            Row(
              children: [
                DropdownButton<String>(
                  value: selectedField,
                  onChanged: (val) {
                    setState(() {
                      selectedField = val!;
                      selectedValue = null;
                    });
                    _loadStats();
                  },
                  items: ['No Filter', 'Organization', 'Service']
                      .map((f) => DropdownMenuItem(value: f, child: Text(f)))
                      .toList(),
                ),
                const SizedBox(width: 10),
                if (selectedField != 'No Filter')
                  DropdownButton<String>(
                    hint: const Text('Select Value'),
                    value: selectedValue != null
                        ? (selectedField == 'Organization'
                              ? orgNames[selectedValue!]
                              : serviceNames[selectedValue!])
                        : null,
                    onChanged: (val) {
                      setState(() {
                        selectedValue =
                            (selectedField == 'Organization'
                                    ? orgNames.entries
                                    : serviceNames.entries)
                                .firstWhere((e) => e.value == val)
                                .key;
                      });
                      _loadStats();
                    },
                    items: dropdownOptions
                        .map((v) => DropdownMenuItem(value: v, child: Text(v)))
                        .toList(),
                  ),
              ],
            ),
            const SizedBox(height: 16),

            // Stat Cards
            Expanded(
              flex: 2,
              child: SingleChildScrollView(
                child: _buildResponsiveStats(context),
              ),
            ),

            const SizedBox(height: 10),
            const Text(
              'Recent Tickets',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),

            // Recent Tickets List
            Expanded(
              flex: 2,
              child: ListView.builder(
                itemCount: recentTickets.length,
                itemBuilder: (context, index) {
                  final ticket = recentTickets[index];
                  final date = (ticket['date'] as Timestamp).toDate();
                  final orgName = orgNames[ticket['org']] ?? ticket['org'];
                  final serviceName =
                      serviceNames[ticket['service']] ?? ticket['service'];

                  return Card(
                    child: ListTile(
                      title: Text('Ticket ID: ${ticket['ticketid']}'),
                      subtitle: Text(
                        'Org: $orgName\nService: $serviceName\nDate: ${DateFormat.yMd().add_jm().format(date)}',
                      ),
                      isThreeLine: true,
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
