import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart' as fb_auth;

class UserProvider with ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final fb_auth.FirebaseAuth _auth = fb_auth.FirebaseAuth.instance;

  Map<String, dynamic>? _userData;
  Map<String, dynamic>? get userData => _userData;

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  /// Sign in with email and password
  Future<String?> signIn(String email, String password) async {
    try {
      _isLoading = true;
      notifyListeners();

      final userCredential = await _auth.signInWithEmailAndPassword(
          email: email, password: password);

      final userEmail = userCredential.user?.email;
      if (userEmail == null) {
        _isLoading = false;
        notifyListeners();
        return 'User email not found';
      }

      // Fetch user details from Firestore 'users' collection
      final doc = await _firestore.collection('users').doc(userEmail).get();

      if (!doc.exists) {
        _isLoading = false;
        notifyListeners();
        return 'User data not found in database';
      }

      _userData = doc.data();
      _isLoading = false;
      notifyListeners();
      return null; // null means success
    } on fb_auth.FirebaseAuthException catch (e) {
      _isLoading = false;
      notifyListeners();
      return e.message ?? 'Login failed';
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      return 'An unknown error occurred';
    }
  }

  /// Sign out current user
  Future<void> signOut() async {
    await _auth.signOut();
    _userData = null;
    notifyListeners();
  }

  /// Get current user email (if logged in)
  String? get currentUserEmail => _auth.currentUser?.email;

  /// Check if user is logged in
  bool get isLoggedIn => _auth.currentUser != null;
}
