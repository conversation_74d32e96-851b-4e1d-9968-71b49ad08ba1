import 'package:flutter/material.dart';

class TicketTile extends StatelessWidget {
  final String ticketId;
  final String title;
  final String subtitle;
  final Widget trailing;
  final VoidCallback onTap;
  final Widget? leading;
  final bool isClosed; // ✅ NEW PARAMETER to check ticket status

  const TicketTile({
    Key? key,
    required this.ticketId,
    required this.title,
    required this.subtitle,
    required this.trailing,
    required this.onTap,
    required this.leading,
    required this.isClosed, // ✅ Default to false
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isClosed
            ? const BorderSide(color: Colors.green, width: 1.5)
            : BorderSide.none,
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.fromLTRB(16, 8, 12, 8),
        leading: isClosed
            ? const Icon(Icons.check_circle, color: Colors.green)
            : leading,
        title: Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: isClosed ? Colors.green.shade800 : null,
          ),
        ),
        subtitle: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(subtitle, style: TextStyle(fontSize: 14)),
            if (isClosed)
              Text(
                'Closed',
                style: TextStyle(color: Colors.green.shade700, fontSize: 12),
              ),
          ],
        ),
        trailing: trailing is Text
            ? Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.end,
                children:
                    (trailing as Text).data?.split(' ').map((part) {
                      return Text(
                        part,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      );
                    }).toList() ??
                    [],
              )
            : trailing,

        onTap: onTap,
        minVerticalPadding: 0,
      ),
    );
  }
}
