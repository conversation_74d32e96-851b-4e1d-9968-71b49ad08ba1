import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class CreateTicketScreen extends StatefulWidget {
  final String org;
  final String email;
  const CreateTicketScreen({super.key, required this.org, required this.email});

  @override
  State<CreateTicketScreen> createState() => _CreateTicketScreenState();
}

class _CreateTicketScreenState extends State<CreateTicketScreen> {
  final _firestore = FirebaseFirestore.instance;
  final _formKey = GlobalKey<FormState>();

  List<Map<String, String>> services = [];
  String? selectedService;
  String ticketId = '';
  String priority = 'Medium';
  String problem = '';
  String workaround = '';
  String comments = '';

  bool isLoading = true;

  final priorities = ['High', 'Medium', 'Low'];

  @override
  void initState() {
    super.initState();
    loadAssignedServices();
  }

  Future<void> loadAssignedServices() async {
    final cltosrvSnap = await _firestore
        .collection('cltosrv')
        .where('clshortcode', isEqualTo: widget.org)
        .get();

    final serviceShortcodes = cltosrvSnap.docs
        .map((doc) => doc['srvshortcode'] as String)
        .toList();

    if (serviceShortcodes.isEmpty) {
      setState(() => isLoading = false);
      return;
    }

    final srvSnap = await _firestore
        .collection('services')
        .where('shortcode', whereIn: serviceShortcodes)
        .get();

    services = srvSnap.docs
        .map(
          (doc) => {
            'shortcode': doc['shortcode'].toString(),
            'name': doc['service'].toString(),
          },
        )
        .toList();

    if (services.length == 1) {
      selectedService = services.first['shortcode'];
      await generateTicketId();
    }

    setState(() => isLoading = false);
  }

  Future<void> generateTicketId() async {
    if (selectedService == null) return;

    final query = await _firestore
        .collection('tickets')
        .where('service', isEqualTo: selectedService)
        .orderBy('ticketid', descending: true)
        .limit(1)
        .get();

    int newNumber = 1;
    if (query.docs.isNotEmpty) {
      final lastId = query.docs.last['ticketid'] as String;
      final parts = lastId.split('-');
      if (parts.length == 2) {
        newNumber = int.tryParse(parts[1]) ?? 0;
        newNumber++;
      }
    }
    setState(() {
      ticketId = '${selectedService!}-${newNumber.toString().padLeft(4, '0')}';
    });
    debugPrint(ticketId);
  }

  Future<void> _submit() async {
    if (!_formKey.currentState!.validate() ||
        selectedService == null ||
        ticketId.isEmpty) {
      return;
    }

    _formKey.currentState!.save();

    final now = Timestamp.now();
    final orgsrv = '${widget.org} ${selectedService!}';

    await _firestore.collection('tickets').add({
      'ticketid': ticketId,
      'org': widget.org,
      'service': selectedService,
      'orgsrv': orgsrv,
      'createdby': widget.email,
      'date': now,
      'priority': priority,
      'problem': problem,
      'workaround': workaround,
      'comments': comments,
      'status': 'Open',
    });

    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Ticket created successfully for Ticket Id: $ticketId!',
          ),
        ),
      );
      Navigator.pop(context, true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Create Ticket')),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: ListView(
                  children: [
                    services.length == 1
                        ? Text(
                            'Service: ${services.first['name']} (${services.first['shortcode']})',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          )
                        : DropdownButtonFormField<String>(
                            value: selectedService,
                            decoration: const InputDecoration(
                              labelText: 'Select Service',
                              border: OutlineInputBorder(),
                            ),
                            items: services
                                .map(
                                  (s) => DropdownMenuItem(
                                    value: s['shortcode'],
                                    child: Text(
                                      '${s['name']} (${s['shortcode']})',
                                    ),
                                  ),
                                )
                                .toList(),
                            onChanged: (val) async {
                              setState(() {
                                selectedService = val;
                                ticketId = '';
                              });
                              await generateTicketId();
                            },
                            validator: (val) =>
                                val == null ? 'Please select service' : null,
                          ),
                    const SizedBox(height: 8),
                    Text(
                      ticketId.isNotEmpty
                          ? 'Generated Ticket ID: $ticketId'
                          : 'Ticket ID will appear after selecting a service',
                      style: const TextStyle(fontSize: 14),
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      value: priority,
                      decoration: const InputDecoration(
                        labelText: 'Priority',
                        border: OutlineInputBorder(),
                      ),
                      items: priorities
                          .map(
                            (p) => DropdownMenuItem(value: p, child: Text(p)),
                          )
                          .toList(),
                      onChanged: (val) => setState(() => priority = val!),
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'Problem Description',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                      validator: (v) =>
                          v!.isEmpty ? 'Please enter problem' : null,
                      onSaved: (v) => problem = v!,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'Workaround (if any)',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 2,
                      onSaved: (v) => workaround = v ?? '',
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'Comments',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 2,
                      onSaved: (v) => comments = v ?? '',
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton.icon(
                      icon: const Icon(Icons.send),
                      label: const Text('Submit Ticket'),
                      onPressed: _submit,
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
