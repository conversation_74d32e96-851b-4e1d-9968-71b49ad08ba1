// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAufvAP40O2Z7CHNykXNAheTM5s1bOpb-4',
    appId: '1:81002433459:web:43876df85d072948df59c6',
    messagingSenderId: '81002433459',
    projectId: 'ticketsys-c1fb9',
    authDomain: 'ticketsys-c1fb9.firebaseapp.com',
    storageBucket: 'ticketsys-c1fb9.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBX3fN3u7NdaEcDMShKZr2PsrSkUVYelGs',
    appId: '1:81002433459:android:88ec36fd7dc0a3aedf59c6',
    messagingSenderId: '81002433459',
    projectId: 'ticketsys-c1fb9',
    storageBucket: 'ticketsys-c1fb9.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyB3JYPtHb_zY60XYz57VYcGxC3mMa_wCq0',
    appId: '1:81002433459:ios:ce45f286bafd1b91df59c6',
    messagingSenderId: '81002433459',
    projectId: 'ticketsys-c1fb9',
    storageBucket: 'ticketsys-c1fb9.firebasestorage.app',
    iosBundleId: 'com.example.ticketsystem',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyB3JYPtHb_zY60XYz57VYcGxC3mMa_wCq0',
    appId: '1:81002433459:ios:ce45f286bafd1b91df59c6',
    messagingSenderId: '81002433459',
    projectId: 'ticketsys-c1fb9',
    storageBucket: 'ticketsys-c1fb9.firebasestorage.app',
    iosBundleId: 'com.example.ticketsystem',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAufvAP40O2Z7CHNykXNAheTM5s1bOpb-4',
    appId: '1:81002433459:web:773475eab2b4e55bdf59c6',
    messagingSenderId: '81002433459',
    projectId: 'ticketsys-c1fb9',
    authDomain: 'ticketsys-c1fb9.firebaseapp.com',
    storageBucket: 'ticketsys-c1fb9.firebasestorage.app',
  );

}