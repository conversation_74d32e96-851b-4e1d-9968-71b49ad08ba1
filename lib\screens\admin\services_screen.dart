import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// ... keep your imports

class AdminServicesScreen extends StatefulWidget {
  const AdminServicesScreen({Key? key}) : super(key: key);

  @override
  State<AdminServicesScreen> createState() => _AdminServicesScreenState();
}

class _AdminServicesScreenState extends State<AdminServicesScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Stream<QuerySnapshot> _getServicesStream() {
    return _firestore.collection('services').snapshots();
  }

  void _openServiceDetail(DocumentSnapshot doc) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (_) => AddEditServiceScreen(document: doc)),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Manage Services')),
      body: StreamBuilder<QuerySnapshot>(
        stream: _getServicesStream(),
        builder: (context, snapshot) {
          if (snapshot.hasError)
            return const Center(child: Text('Error loading services'));
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          final services = snapshot.data!.docs;
          if (services.isEmpty)
            return const Center(child: Text('No services found.'));

          return ListView.builder(
            itemCount: services.length,
            itemBuilder: (context, index) {
              final doc = services[index];
              final data = doc.data() as Map<String, dynamic>;

              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                elevation: 2,
                child: ListTile(
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  title: Text(
                    data['service'] ?? 'Unnamed Service',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 4),
                      Text('Type: ${data['type'] ?? 'N/A'}'),
                    ],
                  ),
                  onTap: () => _openServiceDetail(doc),
                ),
              );
            },
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => Navigator.push(
          context,
          MaterialPageRoute(builder: (_) => const AddEditServiceScreen()),
        ),
        child: const Icon(Icons.add),
        tooltip: 'Add Service',
      ),
    );
  }
}

class AddEditServiceScreen extends StatefulWidget {
  final DocumentSnapshot? document;

  const AddEditServiceScreen({Key? key, this.document}) : super(key: key);

  @override
  State<AddEditServiceScreen> createState() => _AddEditServiceScreenState();
}

class _AddEditServiceScreenState extends State<AddEditServiceScreen> {
  final _formKey = GlobalKey<FormState>();
  final _serviceController = TextEditingController();
  final _shortcodeController = TextEditingController();
  final _typeController = TextEditingController();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  bool isEditing = false;
  bool isEditMode = false;
  String _serviceShortcode = '';

  @override
  void initState() {
    super.initState();
    isEditing = widget.document != null;
    isEditMode = !isEditing;

    if (isEditing) {
      final data = widget.document!.data() as Map<String, dynamic>;
      _serviceController.text = data['service'] ?? '';
      _typeController.text = data['type'] ?? '';
      _shortcodeController.text = data['shortcode'] ?? '';
      _serviceShortcode = data['shortcode'] ?? '';
    }
  }

  void _enableEdit() {
    setState(() {
      isEditMode = true;
    });
  }

  Future<void> _saveService() async {
    if (_formKey.currentState!.validate()) {
      final data = {
        'service': _serviceController.text.trim(),
        'type': _typeController.text.trim(),
        'shortcode': _shortcodeController.text.trim(),
      };

      try {
        if (!isEditing) {
          await _firestore.collection('services').add(data);
        } else {
          await _firestore
              .collection('services')
              .doc(widget.document!.id)
              .update(data);
        }
        Navigator.pop(context);
      } catch (e) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error saving service: $e')));
      }
    }
  }

  Widget _buildField({
    required String label,
    required TextEditingController controller,
    bool editable = true,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: isEditMode && editable
          ? TextFormField(
              controller: controller,
              decoration: InputDecoration(labelText: label),
              validator: (val) =>
                  val == null || val.isEmpty ? 'Enter $label' : null,
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
                const SizedBox(height: 4),
                Text(
                  controller.text.isEmpty ? '—' : controller.text,
                  style: const TextStyle(fontSize: 16),
                ),
              ],
            ),
    );
  }

  Widget _buildEmployeeList() {
    return StreamBuilder<QuerySnapshot>(
      stream: _firestore
          .collection('emptosrv')
          .where('srvshortcode', isEqualTo: _shortcodeController.text)
          .snapshots(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        debugPrint(_shortcodeController.text);
        final docs = snapshot.data?.docs ?? [];
        if (docs.isEmpty) return const Text('No employees assigned.');

        return ListView.builder(
          itemCount: docs.length,
          itemBuilder: (context, index) {
            final data = docs[index].data() as Map<String, dynamic>;
            final empEmail = data['empemail'];

            return FutureBuilder<QuerySnapshot>(
              future: _firestore
                  .collection('users')
                  .where('email', isEqualTo: empEmail)
                  .limit(1)
                  .get(),
              builder: (context, snapshot) {
                if (!snapshot.hasData ||
                    snapshot.data!.docs.isEmpty ||
                    !snapshot.data!.docs.first.exists) {
                  return const SizedBox.shrink();
                }

                final user =
                    snapshot.data!.docs.first.data() as Map<String, dynamic>;

                return Card(
                  margin: const EdgeInsets.symmetric(vertical: 4),
                  child: ListTile(
                    dense: true,
                    leading: const Icon(Icons.person),
                    title: Text(user['name'] ?? 'Unknown'),
                    subtitle: Text(empEmail),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }

  Widget _buildClientList() {
    return StreamBuilder<QuerySnapshot>(
      stream: _firestore
          .collection('cltosrv')
          .where('srvshortcode', isEqualTo: _shortcodeController.text)
          .snapshots(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        final docs = snapshot.data?.docs ?? [];
        if (docs.isEmpty) return const Text('No clients assigned.');

        return ListView.builder(
          itemCount: docs.length,
          itemBuilder: (context, index) {
            final data = docs[index].data() as Map<String, dynamic>;
            final clientShortcode = data['clshortcode'];
            debugPrint(data.toString());

            return FutureBuilder<QuerySnapshot>(
              future: _firestore
                  .collection('clients')
                  .where('shortcode', isEqualTo: clientShortcode)
                  .limit(1)
                  .get(),
              builder: (context, snapshot) {
                if (!snapshot.hasData ||
                    snapshot.data!.docs.isEmpty ||
                    !snapshot.data!.docs.first.exists) {
                  return const SizedBox.shrink();
                }

                final client =
                    snapshot.data!.docs.first.data() as Map<String, dynamic>;

                return Card(
                  margin: const EdgeInsets.symmetric(vertical: 4),
                  child: ListTile(
                    dense: true,
                    leading: const Icon(Icons.business),
                    title: Text(client['name'] ?? 'Unknown'),
                    subtitle: Text(clientShortcode),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }

  @override
  void dispose() {
    _serviceController.dispose();
    _shortcodeController.dispose();
    _typeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? 'Service Details' : 'Add Service'),
        actions: [
          if (isEditing && !isEditMode)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: _enableEdit,
              tooltip: 'Edit',
            ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              _buildField(
                label: 'Service Name',
                controller: _serviceController,
              ),
              _buildField(label: 'Type', controller: _typeController),
              //const SizedBox(height: 10),
              _buildField(
                label: 'Short Code',
                controller: _shortcodeController,
                editable: !isEditing,
              ),
              if (isEditMode)
                ElevatedButton(
                  onPressed: _saveService,
                  child: Text(isEditing ? 'Update' : 'Add'),
                ),
              if (isEditing) ...[
                const SizedBox(height: 24),
                //const Divider(),
                const SizedBox(height: 12),
                const Text(
                  'Assignments',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),
                SizedBox(
                  height:
                      MediaQuery.of(context).size.height *
                      0.5, // Adjust as needed
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // LEFT COLUMN
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Employees',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            const Divider(thickness: 1, color: Colors.grey),
                            const SizedBox(height: 8),
                            Expanded(child: _buildEmployeeList()),
                          ],
                        ),
                      ),
                      // CENTER VERTICAL DIVIDER
                      const VerticalDivider(
                        width: 20,
                        thickness: 1,
                        color: Colors.grey,
                      ),
                      // RIGHT COLUMN
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Clients',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            const Divider(thickness: 1, color: Colors.grey),
                            const SizedBox(height: 8),
                            Expanded(child: _buildClientList()),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
