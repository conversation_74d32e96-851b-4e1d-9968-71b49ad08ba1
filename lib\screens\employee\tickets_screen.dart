import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';

class EmployeeTicketsScreen extends StatefulWidget {
  final String email;

  const EmployeeTicketsScreen({Key? key, required this.email}) : super(key: key);

  @override
  State<EmployeeTicketsScreen> createState() => _EmployeeTicketsScreenState();
}

class _EmployeeTicketsScreenState extends State<EmployeeTicketsScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  Stream<QuerySnapshot> _getAssignedTickets() {
    return _firestore
        .collection('tickets')
        .where('assignedTo', isEqualTo: widget.email)
        .snapshots();
  }

  void _showTicketDetails(DocumentSnapshot document) {
    final data = document.data() as Map<String, dynamic>;

    String formatDate(Timestamp? timestamp) {
      if (timestamp == null) return 'N/A';
      return DateFormat('yyyy-MM-dd – kk:mm').format(timestamp.toDate());
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (_) {
        return Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 32),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Ticket ID: ${data['ticketid'] ?? 'N/A'}",
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 10),
                Text("Problem: ${data['problem'] ?? 'N/A'}"),
                const SizedBox(height: 8),
                Text("Workaround: ${data['workaround'] ?? 'N/A'}"),
                const SizedBox(height: 8),
                Text("Priority: ${data['priority'] ?? 'N/A'}"),
                const SizedBox(height: 8),
                Text("Service: ${data['service'] ?? 'N/A'}"),
                const SizedBox(height: 8),
                Text("Organization: ${data['org'] ?? 'N/A'}"),
                const SizedBox(height: 8),
                Text("Client Email: ${data['createdby'] ?? 'N/A'}"),
                const SizedBox(height: 8),
                Text("Date: ${formatDate(data['date'])}"),
                const SizedBox(height: 8),
                Text("Comments: ${data['comments'] ?? 'None'}"),
                const SizedBox(height: 20),
                Center(
                  child: ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text("Close"),
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTicketTile(DocumentSnapshot document) {
    final data = document.data() as Map<String, dynamic>;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      child: ListTile(
        title: Text(data['problem'] ?? 'No Title'),
        subtitle: Text("Priority: ${data['priority'] ?? 'N/A'} • Service: ${data['service'] ?? 'N/A'}"),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () => _showTicketDetails(document),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("Assigned Tickets")),
      body: StreamBuilder<QuerySnapshot>(
        stream: _getAssignedTickets(),
        builder: (context, snapshot) {
          if (snapshot.hasError) return const Center(child: Text('Error loading tickets'));
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          final tickets = snapshot.data!.docs;
          if (tickets.isEmpty) {
            return const Center(child: Text('No tickets assigned to you.'));
          }

          return ListView.builder(
            itemCount: tickets.length,
            itemBuilder: (context, index) {
              return _buildTicketTile(tickets[index]);
            },
          );
        },
      ),
    );
  }
}
