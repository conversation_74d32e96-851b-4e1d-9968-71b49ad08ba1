import 'package:flutter/material.dart';

import '../screens/admin/dashboard_screen.dart';
import '../screens/admin/tickets_screen.dart';
import '../screens/admin/clients_screen.dart';
import '../screens/admin/employees_screen.dart';
import '../screens/admin/services_screen.dart';

import '../screens/employee/dashboard_screen.dart';
import '../screens/employee/tickets_screen.dart';
import '../screens/employee/clients_screen.dart';

import '../screens/client/dashboard_screen.dart';
import '../screens/client/tickets_screen.dart';
import '../screens/client/users_screen.dart';

class RoleBasedBottomNavBar extends StatefulWidget {
  final String role;
  final String email;
  final String? org; // org is optional and only for clients

  const RoleBasedBottomNavBar({
    super.key,
    required this.role,
    required this.email,
    this.org,
  });

  @override
  State<RoleBasedBottomNavBar> createState() => _RoleBasedBottomNavBarState();
}

class _RoleBasedBottomNavBarState extends State<RoleBasedBottomNavBar> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    final role = widget.role.toLowerCase();

    List<Widget> _adminScreens = [
      AdminDashboardScreen(email: widget.email),
      AdminTicketsScreen(email: widget.email),
      AdminClientsScreen(),
      AdminEmployeesScreen(),
      AdminServicesScreen(),
    ];

    List<BottomNavigationBarItem> _adminNavItems = [
      const BottomNavigationBarItem(
        icon: Icon(Icons.dashboard),
        label: 'Dashboard',
      ),
      const BottomNavigationBarItem(
        icon: Icon(Icons.confirmation_number),
        label: 'Tickets',
      ),
      const BottomNavigationBarItem(icon: Icon(Icons.people), label: 'Clients'),
      const BottomNavigationBarItem(
        icon: Icon(Icons.engineering),
        label: 'Employees',
      ),
      const BottomNavigationBarItem(
        icon: Icon(Icons.miscellaneous_services),
        label: 'Services',
      ),
    ];

    List<Widget> _employeeScreens = [
      EmployeeDashboardScreen(email: widget.email),
      EmployeeTicketsScreen(email: widget.email),
      EmployeeClientsScreen(email: widget.email),
    ];

    List<BottomNavigationBarItem> _employeeNavItems = [
      const BottomNavigationBarItem(
        icon: Icon(Icons.dashboard),
        label: 'Dashboard',
      ),
      const BottomNavigationBarItem(
        icon: Icon(Icons.confirmation_number),
        label: 'Tickets',
      ),
      const BottomNavigationBarItem(icon: Icon(Icons.people), label: 'Clients'),
    ];

    List<Widget> _clientScreens = [
      ClientDashboardScreen(email: widget.email, org: widget.org ?? ''),
      ClientTicketsScreen(email: widget.email, org: widget.org ?? ''),
      ClientUsersScreen(email: widget.email, org: widget.org ?? ''),
    ];

    List<BottomNavigationBarItem> _clientNavItems = [
      const BottomNavigationBarItem(
        icon: Icon(Icons.dashboard),
        label: 'Dashboard',
      ),
      const BottomNavigationBarItem(
        icon: Icon(Icons.confirmation_number),
        label: 'Tickets',
      ),
      const BottomNavigationBarItem(
        icon: Icon(Icons.people_alt),
        label: 'Users',
      ),
    ];

    final List<Widget> currentScreens;
    final List<BottomNavigationBarItem> currentNavItems;

    if (role == 'admin') {
      currentScreens = _adminScreens;
      currentNavItems = _adminNavItems;
    } else if (role == 'employee') {
      currentScreens = _employeeScreens;
      currentNavItems = _employeeNavItems;
    } else {
      currentScreens = _clientScreens;
      currentNavItems = _clientNavItems;
    }
    return Scaffold(
      body: IndexedStack(index: _currentIndex, children: currentScreens),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        items: currentNavItems.map((item) {
          return BottomNavigationBarItem(
            icon: Padding(
              padding: const EdgeInsets.only(top: 8.0), // moves both up
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  item.icon,
                  const SizedBox(height: 2), // spacing between icon and label
                  Text(
                    item.label ?? '',
                    style: const TextStyle(fontSize: 12), // customize if needed
                  ),
                ],
              ),
            ),
            label: '', // Clear default label
          );
        }).toList(),
        onTap: (index) => setState(() {
          _currentIndex = index;
        }),
        selectedItemColor: Theme.of(context).primaryColor,
        unselectedItemColor: Colors.grey,
        type: BottomNavigationBarType.fixed,
      ),
    );
  }
}
